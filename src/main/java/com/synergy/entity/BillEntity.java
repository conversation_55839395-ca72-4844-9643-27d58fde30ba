package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.math.BigDecimal;

@Entity
@Table(name = "bills")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BillEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "bill_number", unique = true, nullable = false)
    private String billNumber; // Format: PO{vendorPoId}-GN{sequence} or BILL{sequence}
    
    @Column(name = "grn_id", nullable = false)
    private Long grnId;
    
    @Column(name = "grn_number", nullable = false)
    private String grnNumber;
    
    @Column(name = "vendor_po_id", nullable = false)
    private String vendorPoId;
    
    @Column(name = "yard_number")
    private String yardNumber;
    
    @Column(name = "project_name")
    private String projectName;
    
    @Column(name = "vendor_company_name")
    private String vendorCompanyName;
    
    @Column(name = "total_amount", precision = 18, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(name = "status")
    private String status; // DRAFT, PENDING, APPROVED, PAID
    
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;
    
    @Column(name = "updated_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedDate;
    
    @Column(name = "approved_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date approvedDate;
    
    @Column(name = "paid_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date paidDate;
    
    @Column(name = "remarks")
    private String remarks;
    
    @OneToMany(mappedBy = "bill", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<BillLineItemEntity> lineItems = new ArrayList<>();
    
    @PrePersist
    protected void onCreate() {
        createdDate = new Date();
        if (status == null) {
            status = "DRAFT";
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedDate = new Date();
    }
}
