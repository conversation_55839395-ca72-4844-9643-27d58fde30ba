package com.synergy.service;

import com.synergy.dto.PurchaseRequestDTO;
import com.synergy.dto.PurchaseRequestItemDTO;
import com.synergy.dto.PurchaseRequestLightDTO;
import com.synergy.dto.VendorRfqStatusDTO;
import com.synergy.dto.ApprovedPODTO;
import com.synergy.dto.ApprovedPOLineItemDTO;
import com.synergy.dto.PaymentRevisionDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.PurchaseRequestItemEntity;
import com.synergy.entity.MaterialRequisitionEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.entity.VendorQuotationEntity;
import com.synergy.entity.VendorQuotationItemEntity;
import com.synergy.entity.VendorPaymentRevisionEntity;
import com.synergy.entity.ContractorEntity;
import com.synergy.mapper.PurchaseRequestMapper;
import com.synergy.repository.PurchaseRequestRepository;
import com.synergy.repository.VendorRepository;
import com.synergy.repository.VendorQuotationRepository;
import com.synergy.repository.VendorPaymentRevisionRepository;
import com.synergy.repository.MaterialRequisitionRepository;
import com.synergy.repository.ContractorRepository;
import com.synergy.repository.ShipbuildersItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import com.synergy.entity.ShipbuildersItemEntity;

@Service
public class PurchaseRequestService {

    @Value("${app.frontend.url}")
    private String frontendUrl;

    @Value("${app.bidding-form.path}")
    private String biddingFormPath;

    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;

    @Autowired
    private VendorRepository vendorRepository;

    @Autowired
    private ContractorRepository contractorRepository;

    // This repository is used in other parts of the service
    @Autowired
    private MaterialRequisitionRepository materialRequisitionRepository;

    @Autowired
    private VendorQuotationRepository vendorQuotationRepository;

    @Autowired
    private VendorPaymentRevisionRepository vendorPaymentRevisionRepository;

    @Autowired
    private VendorBiddingTokenService tokenService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    public List<PurchaseRequestDTO> getPurchaseRequests() {
        // Return all purchase requests without pagination
        return getAllPurchaseRequests();
    }

    public List<PurchaseRequestDTO> getAllPurchaseRequests() {
        List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findAllByOrderByCreatedDateDesc();

        return purchaseRequests.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public List<PurchaseRequestDTO> getPurchaseRequestsByStatus(String status) {
        // Return all purchase requests by status without pagination
        return getAllPurchaseRequestsByStatus(status);
    }

    public List<PurchaseRequestDTO> getAllPurchaseRequestsByStatus(String status) {
        List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findByStatus(status);

        return purchaseRequests.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get purchase requests for PR Creation workflow (PENDING_APPROVAL or PENDING_VENDOR_SELECTION)
     * Uses ultra-optimized native SQL query for maximum performance
     * Avoids N+1 query problem by using JOINs in the SQL query
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsForCreation() {
        // Use the optimized native SQL query that fetches all data in a single query
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsForCreationNative();
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOsForCreation(nativeResults);
    }

    /**
     * Get purchase requests for RFQ Comparison workflow (PENDING_APPROVAL for both Regular and Instant types)
     * This method returns only essential fields for dashboard view: ID, PR ID, Date, Client name,
     * Yard number, Project name, Line items count, and Status
     * Only returns PRs where vendors have been selected
     * Uses ultra-optimized native SQL query for maximum performance
     * Avoids N+1 query problem by using JOINs in the SQL query
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsForRfqComparison() {
        // Use the optimized native SQL query that fetches all data in a single query
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByStatusWithVendorsNative("PENDING_APPROVAL");
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests for Manager In-Progress workflow (PENDING_APPROVAL)
     * Only returns PRs where vendors have been selected
     * Returns a lightweight response with only essential fields for better performance
     * Uses ultra-optimized native SQL query for maximum performance
     * Avoids N+1 query problem by using JOINs in the SQL query
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsForManager() {
        // Use the optimized native SQL query that fetches all data in a single query
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByStatusWithVendorsNative("PENDING_APPROVAL");
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests for In-Progress Management workflow
     * Returns PRs with status IN_PROGRESS_MANAGEMENT
     * This is used for the Management part of the workflow where PRs are in progress
     * Returns a lightweight response with only essential fields for better performance
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsForInProgressManagement() {
        // Use the optimized native SQL query that fetches all data in a single query
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsForInProgressManagementNative();
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOsForInProgressManagement(nativeResults);
    }

    /**
     * Get purchase requests for Internal workflow
     * Returns PRs where quantity has been changed
     * This is used for the Internal part of the workflow where quantity changes need to be reviewed
     * Returns a complete response with all details including line items with quantity changes
     */
    public List<PurchaseRequestDTO> getPurchaseRequestsForInternal() {
        // Get PRs with status PENDING_QUANTITY_APPROVAL
        List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findByStatus("PENDING_QUANTITY_APPROVAL");

        // Filter to only include PRs where quantity has been updated
        List<PurchaseRequestEntity> filteredRequests = purchaseRequests.stream()
                .filter(pr -> pr.getQuantityUpdated() != null && pr.getQuantityUpdated())
                .collect(Collectors.toList());

        return filteredRequests.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get purchase requests by date range with optional status, prType and section filtering
     * This method supports different sections like 'rfq-comparison', 'creation', 'manager', etc.
     * Returns full DTOs for complete data or light DTOs for dashboard views based on section
     */
    public List<?> getPurchaseRequestsByDateRange(Date fromDate, Date toDate, String status, String prType, String section) {
        // Validate date range
        if (fromDate == null || toDate == null) {
            throw new IllegalArgumentException("Both fromDate and toDate are required");
        }

        if (fromDate.after(toDate)) {
            throw new IllegalArgumentException("fromDate cannot be after toDate");
        }

        // Adjust dates for proper filtering
        // fromDate: set to start of day (00:00:00.000)
        // toDate: set to end of day (23:59:59.999)
        Date adjustedFromDate = adjustToStartOfDay(fromDate);
        Date adjustedToDate = adjustToEndOfDay(toDate);

        // Handle different sections
        if (section != null) {
            switch (section.toLowerCase()) {
                case "rfq-comparison":
                    return getPurchaseRequestsByDateRangeForRfqComparison(adjustedFromDate, adjustedToDate, status, prType);
                case "creation":
                    return getPurchaseRequestsByDateRangeForCreation(adjustedFromDate, adjustedToDate, status, prType);
                case "manager":
                    return getPurchaseRequestsByDateRangeForManager(adjustedFromDate, adjustedToDate, status, prType);
                case "in-progress-management":
                    return getPurchaseRequestsByDateRangeForInProgressManagement(adjustedFromDate, adjustedToDate, status, prType);
                case "internal":
                    return getPurchaseRequestsByDateRangeForInternal(adjustedFromDate, adjustedToDate, status, prType);
                default:
                    // For unknown sections, return light DTOs
                    return getPurchaseRequestsByDateRangeLight(adjustedFromDate, adjustedToDate, status, prType);
            }
        }

        // Default: return full DTOs
        return getPurchaseRequestsByDateRangeFull(adjustedFromDate, adjustedToDate, status, prType);
    }

    /**
     * Get purchase requests by date range for RFQ Comparison workflow
     * Returns light DTOs with RFQ status information
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsByDateRangeForRfqComparison(Date fromDate, Date toDate, String status, String prType) {
        String effectiveStatus = (status != null && !status.trim().isEmpty()) ? status : "PENDING_APPROVAL";
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByDateRangeAndStatusAndPrTypeWithVendorsNative(fromDate, toDate, effectiveStatus, prType);
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests by date range for PR Creation workflow
     * Returns light DTOs for dashboard view
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsByDateRangeForCreation(Date fromDate, Date toDate, String status, String prType) {
        String effectiveStatus = (status != null && !status.trim().isEmpty()) ? status : "PENDING_VENDOR_SELECTION";
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByDateRangeAndStatusAndPrTypeNative(fromDate, toDate, effectiveStatus, prType);
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests by date range for Manager workflow
     * Returns light DTOs with RFQ status information
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsByDateRangeForManager(Date fromDate, Date toDate, String status, String prType) {
        String effectiveStatus = (status != null && !status.trim().isEmpty()) ? status : "PENDING_APPROVAL";
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByDateRangeAndStatusAndPrTypeWithVendorsNative(fromDate, toDate, effectiveStatus, prType);
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests by date range for In-Progress Management workflow
     * Returns light DTOs for dashboard view
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsByDateRangeForInProgressManagement(Date fromDate, Date toDate, String status, String prType) {
        String effectiveStatus = (status != null && !status.trim().isEmpty()) ? status : "IN_PROGRESS_MANAGEMENT";
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByDateRangeAndStatusAndPrTypeNative(fromDate, toDate, effectiveStatus, prType);
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests by date range for Internal workflow
     * Returns full DTOs with complete line item information
     */
    public List<PurchaseRequestDTO> getPurchaseRequestsByDateRangeForInternal(Date fromDate, Date toDate, String status, String prType) {
        String effectiveStatus = (status != null && !status.trim().isEmpty()) ? status : "PENDING_QUANTITY_APPROVAL";
        List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findByCreatedDateBetweenAndStatusAndPrType(fromDate, toDate, effectiveStatus, prType);

        // Filter to only include PRs where quantity has been updated
        List<PurchaseRequestEntity> filteredRequests = purchaseRequests.stream()
                .filter(pr -> pr.getQuantityUpdated() != null && pr.getQuantityUpdated())
                .collect(Collectors.toList());

        return filteredRequests.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get purchase requests by date range - light DTOs for dashboard views
     */
    public List<PurchaseRequestLightDTO> getPurchaseRequestsByDateRangeLight(Date fromDate, Date toDate, String status, String prType) {
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByDateRangeAndStatusAndPrTypeNative(fromDate, toDate, status, prType);
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get purchase requests by date range - full DTOs with complete information
     */
    public List<PurchaseRequestDTO> getPurchaseRequestsByDateRangeFull(Date fromDate, Date toDate, String status, String prType) {
        List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findByCreatedDateBetweenAndStatusAndPrType(fromDate, toDate, status, prType);

        return purchaseRequests.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public PurchaseRequestDTO getPurchaseRequestById(Long prId) {
        PurchaseRequestEntity entity = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));
        return convertToDTO(entity);
    }

    @Transactional
    public PurchaseRequestDTO createPurchaseRequest(PurchaseRequestDTO dto) {
        try {
            System.out.println("Starting purchase request creation with DTO: " + dto);
            validatePurchaseRequest(dto);
            validateNoDuplicateLineItems(dto.getLineItems());
            System.out.println("Validation passed");

            PurchaseRequestEntity entity = new PurchaseRequestEntity();
            entity.setPrId(generatePRId());
            entity.setYardNumber(dto.getYardNumber());
            entity.setProjectName(dto.getProjectName());
            entity.setCreatedDate(new Date());
            entity.setPrType(dto.getPrType());
            entity.setStatus("PENDING_APPROVAL");
            entity.setQuantityUpdated(dto.getPrType().equals("REGULAR") ? false : null);

            // Set remarks if provided
            if (dto.getRemarks() != null && !dto.getRemarks().trim().isEmpty()) {
                entity.setRemarks(dto.getRemarks().trim());
            }

            // Set the contractorName field (client name) if provided
            if (dto.getContractorName() != null && !dto.getContractorName().trim().isEmpty()) {
                entity.setContractorName(dto.getContractorName());
                System.out.println("Client name (contractorName) set to: " + dto.getContractorName());
            }

            System.out.println("Basic entity properties set");

            // Find contractors by contractor names
            List<String> contractorNames = dto.getContractorNames().stream()
                    .filter(Objects::nonNull)
                    .map(String::trim)
                    .collect(Collectors.toList());

            List<ContractorEntity> contractors = contractorRepository.findAll().stream()
                    .filter(contractor -> {
                        String name = contractor.getVendorName();
                        return name != null &&
                                !name.trim().isEmpty() &&
                                contractorNames.contains(name.trim());
                    })
                    .collect(Collectors.toList());

            if (contractors.isEmpty()) {
                System.out.println("No contractors found with the provided names: " + contractorNames);
                System.out.println("Available contractor names: " +
                        contractorRepository.findAll().stream()
                                .map(ContractorEntity::getVendorName)
                                .collect(Collectors.toList()));
                throw new IllegalArgumentException("No contractors found with the provided names: " + contractorNames);
            }

            // Validate contractor count based on PR type
            if (dto.getPrType().equals("REGULAR") && contractors.size() == 0) {
                throw new IllegalArgumentException("Regular PR must have at least 1 contractor");
            }
            if (dto.getPrType().equals("INSTANT") && contractors.size() == 0) {
                throw new IllegalArgumentException("Instant PR must have at least 1 contractor");
            }
            entity.setContractors(contractors);

            // Find vendors by vendor names
            List<String> vendorNames = dto.getVendorNames().stream()
                    .filter(Objects::nonNull)
                    .map(String::trim)
                    .collect(Collectors.toList());

            List<VendorEntity> vendors = vendorRepository.findAll().stream()
                    .filter(vendor -> {
                        String name = vendor.getCompanyName();
                        return name != null &&
                                !name.trim().isEmpty() &&
                                vendorNames.contains(name.trim());
                    })
                    .collect(Collectors.toList());

            if (vendors.isEmpty()) {
                throw new IllegalArgumentException("No vendors found with the provided names: " + vendorNames);
            }

            if (dto.getPrType().equals("REGULAR") && vendors.size() < 2) {
                throw new IllegalArgumentException("Regular PR must have at least 2 vendors");
            }
            if (dto.getPrType().equals("INSTANT") && vendors.size() != 1) {
                throw new IllegalArgumentException("Instant PR must have exactly 1 vendor");
            }
            entity.setVendors(vendors);

            // Set line items
            List<PurchaseRequestItemEntity> lineItems = dto.getLineItems().stream()
                    .map(itemDTO -> {
                        PurchaseRequestItemEntity item = new PurchaseRequestItemEntity();
                        item.setUniqueCode(itemDTO.getUniqueCode());
                        item.setProductName(itemDTO.getProductName());
                        item.setUnitOfMeasure(itemDTO.getUnitOfMeasure());
                        item.setQuantity(itemDTO.getQuantity());
                        item.setRate(itemDTO.getRate());
                        item.setTotal(itemDTO.getRate().multiply(BigDecimal.valueOf(itemDTO.getQuantity())));
                        item.setItemName(itemDTO.getItemName());
                        item.setMaterialFamily(getMaterialFamilyByUniqueCode(itemDTO.getUniqueCode()));

                        // Handle quantity changes only for REGULAR PRs
                        if (dto.getPrType().equals("REGULAR")) {
                            if (itemDTO.getChangeInQuantity() != null && !itemDTO.getChangeInQuantity().equals(0)) {
                                item.setChangeInQuantity(itemDTO.getChangeInQuantity());
                                item.setQuantityUpdated(true);
                                // Update the actual quantity
                                item.setQuantity(item.getQuantity() + itemDTO.getChangeInQuantity());
                                // Recalculate total
                                item.setTotal(item.getRate().multiply(BigDecimal.valueOf(item.getQuantity())));
                            } else {
                                item.setChangeInQuantity(0);
                                item.setQuantityUpdated(false);
                            }
                        } else {
                            // For instant PRs, set these to null
                            item.setChangeInQuantity(null);
                            item.setQuantityUpdated(null);
                        }

                        item.setPurchaseRequest(entity);
                        return item;
                    })
                    .collect(Collectors.toList());
            entity.setLineItems(lineItems);

            // Calculate grand total for instant PRs
            if (dto.getPrType().equals("INSTANT")) {
                BigDecimal grandTotal = lineItems.stream()
                        .map(PurchaseRequestItemEntity::getTotal)
                        .filter(total -> total != null)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                entity.setGrandTotal(grandTotal);
                System.out.println("Calculated grand total for instant PR: " + grandTotal);
            }

            // Set status based on PR type
            if (dto.getPrType().equals("INSTANT")) {
                entity.setStatus("IN_PROGRESS_MANAGEMENT");
            } else {
                entity.setStatus("PENDING_APPROVAL");
            }

            PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);

            // Send email to each vendor with configurable URL - only for REGULAR PRs
            if (!"INSTANT".equals(entity.getPrType())) {
                String baseFormUrl = frontendUrl + biddingFormPath;
                System.out.println("Sending bidding links to vendors for PR ID: " + savedEntity.getId());
                for (VendorEntity vendor : vendors) {
                    try {
                        // Generate unique token for this vendor and PR
                        String token = tokenService.generateBiddingToken(savedEntity, vendor);
                        String formUrl = baseFormUrl + "?token=" + token;

                        System.out.println("Sending bidding link to vendor: " + vendor.getCompanyName() + " (" + vendor.getEmailId() + ")");
                        System.out.println("Bidding link with token: " + formUrl);

                        emailService.sendQuotationRequestEmail(
                                vendor.getEmailId(),
                                vendor.getCompanyName(),
                                savedEntity.getId(),
                                formUrl
                        );
                        System.out.println("Email sent successfully to vendor: " + vendor.getCompanyName());
                    } catch (Exception e) {
                        System.err.println("Error sending email to vendor " + vendor.getCompanyName() + ": " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            } else {
                System.out.println("Skipping sending bidding links for INSTANT PR ID: " + savedEntity.getId());
            }

            return convertToDTO(savedEntity);
        } catch (Exception e) {
            System.err.println("Error creating purchase request: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Transactional
    public void createPurchaseRequestFromMR(MaterialRequisitionEntity mr) {
        PurchaseRequestEntity pr = new PurchaseRequestEntity();
        pr.setPrId(generatePRId());
        pr.setMaterialRequisition(mr);
        pr.setCreatedDate(new Date());
        pr.setStatus("PENDING_VENDOR_SELECTION");
        pr.setPrType("REGULAR"); // MR-based PRs are always regular

        // Copy contractor information from MR
        if (mr.getContractors() != null && !mr.getContractors().isEmpty()) {
            pr.setContractors(mr.getContractors().stream().collect(Collectors.toList()));
        } else {
            // If no contractors in MR, find contractor by name
            String contractorName = mr.getContractorName();
            if (contractorName != null && !contractorName.trim().isEmpty()) {
                List<ContractorEntity> contractors = contractorRepository.findAll().stream()
                    .filter(contractor -> contractor.getVendorName() != null &&
                        contractor.getVendorName().trim().equalsIgnoreCase(contractorName.trim()))
                    .collect(Collectors.toList());
                if (!contractors.isEmpty()) {
                    pr.setContractors(contractors);
                }
            }
        }

        // Set the contractorName field (client name) from the MR
        if (mr.getContractorName() != null && !mr.getContractorName().trim().isEmpty()) {
            pr.setContractorName(mr.getContractorName());
            System.out.println("Client name (contractorName) set from MR to: " + mr.getContractorName());
        }

        pr.setYardNumber(mr.getYardNumber());
        pr.setProjectName(mr.getProjectName());

        // Copy line items from MR to PR
        if (mr.getLineItems() != null && !mr.getLineItems().isEmpty()) {
            List<PurchaseRequestItemEntity> prLineItems = mr.getLineItems().stream()
                    .map(mrItem -> {
                        PurchaseRequestItemEntity prItem = new PurchaseRequestItemEntity();
                        prItem.setUniqueCode(mrItem.getUniqueCode());
                        prItem.setProductName(mrItem.getProductName());
                        prItem.setUnitOfMeasure(mrItem.getUnitOfMeasure());
                        // Convert Double to Integer for quantity
                        prItem.setQuantity(mrItem.getQuantity() != null ? mrItem.getQuantity().intValue() : 0);
                        // Set default values for rate and total since they don't exist in MR items
                        prItem.setRate(BigDecimal.ZERO);
                        prItem.setTotal(BigDecimal.ZERO);
                        prItem.setMaterialFamily(getMaterialFamilyByUniqueCode(mrItem.getUniqueCode()));
                        prItem.setPurchaseRequest(pr);
                        return prItem;
                    })
                    .collect(Collectors.toList());
            pr.setLineItems(prLineItems);
        }

        // If MR already has vendors assigned, copy them to the PR
        if (mr.getVendors() != null && !mr.getVendors().isEmpty()) {
            pr.setVendors(mr.getVendors().stream().collect(Collectors.toList()));
        }

        purchaseRequestRepository.save(pr);
    }

    /**
     * Synchronize Purchase Request line items from its source Material Requisition
     * Only syncs if PR is in PENDING_VENDOR_SELECTION status (before vendor selection)
     */
    @Transactional
    public void syncPurchaseRequestFromMR(MaterialRequisitionEntity mr) {
        if (mr.getPurchaseRequest() == null) {
            return; // No associated PR to sync
        }

        PurchaseRequestEntity pr = mr.getPurchaseRequest();

        // Only sync if PR is still in PENDING_VENDOR_SELECTION status
        if (!"PENDING_VENDOR_SELECTION".equals(pr.getStatus())) {
            return; // PR has moved beyond vendor selection, don't sync
        }

        // Create new line items from MR
        List<PurchaseRequestItemEntity> newPrLineItems = new ArrayList<>();
        if (mr.getLineItems() != null && !mr.getLineItems().isEmpty()) {
            newPrLineItems = mr.getLineItems().stream()
                    .map(mrItem -> {
                        PurchaseRequestItemEntity prItem = new PurchaseRequestItemEntity();
                        prItem.setUniqueCode(mrItem.getUniqueCode());
                        prItem.setProductName(mrItem.getProductName());
                        prItem.setUnitOfMeasure(mrItem.getUnitOfMeasure());
                        // Convert Double to Integer for quantity
                        prItem.setQuantity(mrItem.getQuantity() != null ? mrItem.getQuantity().intValue() : 0);
                        // Set default values for rate and total since they don't exist in MR items
                        prItem.setRate(BigDecimal.ZERO);
                        prItem.setTotal(BigDecimal.ZERO);
                        prItem.setMaterialFamily(getMaterialFamilyByUniqueCode(mrItem.getUniqueCode()));
                        prItem.setPurchaseRequest(pr);
                        return prItem;
                    })
                    .collect(Collectors.toList());
        }

        // Clear existing line items and set new ones
        pr.getLineItems().clear();
        pr.getLineItems().addAll(newPrLineItems);

        purchaseRequestRepository.save(pr);
        System.out.println("Synced PR " + pr.getPrId() + " line items from MR " + mr.getRequisitionId() +
                          " - New item count: " + (pr.getLineItems() != null ? pr.getLineItems().size() : 0));
    }

    /**
     * Update a purchase request specifically for the PR Creation workflow
     * This method is used to update PRs that are in the PENDING_VENDOR_SELECTION status
     * It validates that the PR is in the correct status before allowing updates
     */
    @Transactional
    public PurchaseRequestDTO updatePurchaseRequestForCreation(Long prId, PurchaseRequestDTO dto) {
        PurchaseRequestEntity entity = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in the correct status
        if (!"PENDING_VENDOR_SELECTION".equals(entity.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in PENDING_VENDOR_SELECTION status to be updated in the PR Creation workflow");
        }

        // Proceed with the update using the existing method
        return updatePurchaseRequest(prId, dto);
    }

    @Transactional
    public PurchaseRequestDTO updatePurchaseRequest(Long prId, PurchaseRequestDTO dto) {
        PurchaseRequestEntity entity = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));

        // Store the original status to check if it was explicitly changed
        String originalStatus = entity.getStatus();
        String requestedStatus = dto.getStatus();

        System.out.println("Updating PR " + prId + ": Original status=" + originalStatus + ", Requested status=" + requestedStatus);

        // Validate vendor count based on PR type
        if (dto.getVendorNames() == null || dto.getVendorNames().isEmpty()) {
            throw new IllegalArgumentException("At least one vendor is required");
        }

        // Find vendors by company names
        List<String> vendorNames = dto.getVendorNames();
        List<VendorEntity> vendors = vendorRepository.findAll().stream()
                .filter(vendor -> vendor.getCompanyName() != null && !vendor.getCompanyName().isEmpty() &&
                        vendorNames.contains(vendor.getCompanyName()))
                .collect(Collectors.toList());

        if (vendors.isEmpty()) {
            System.out.println("No vendors found with the provided names: " + vendorNames);
            System.out.println("Available vendor names: " +
                    vendorRepository.findAll().stream()
                            .map(VendorEntity::getCompanyName)
                            .collect(Collectors.toList()));
            throw new IllegalArgumentException("No vendors found with the provided names: " + vendorNames);
        }

        if ("REGULAR".equals(entity.getPrType()) && vendors.size() < 2) {
            throw new IllegalArgumentException("Regular PR requires at least 2 vendors");
        }
        if ("INSTANT".equals(entity.getPrType()) && vendors.size() != 1) {
            throw new IllegalArgumentException("Instant PR requires exactly 1 vendor");
        }

        // Update vendor information
        entity.setVendors(vendors);

        // Validate contractor count based on PR type
        if (dto.getContractorNames() == null || dto.getContractorNames().isEmpty()) {
            throw new IllegalArgumentException("At least two contractors are required");
        }

        if ("REGULAR".equals(entity.getPrType()) && dto.getContractorNames().size() == 0) {
            throw new IllegalArgumentException("Regular PR requires at least 1 contractor");
        }

        if ("INSTANT".equals(entity.getPrType()) && dto.getContractorNames().size() == 0) {
            throw new IllegalArgumentException("Instant PR requires at least 1 contractor");
        }

        // Update contractor information
        List<String> contractorNames = dto.getContractorNames();
        List<ContractorEntity> contractors = contractorRepository.findAll().stream()
                .filter(contractor -> contractor.getVendorName() != null && !contractor.getVendorName().isEmpty() &&
                        contractorNames.contains(contractor.getVendorName()))
                .collect(Collectors.toList());

        if (contractors.isEmpty()) {
            System.out.println("No contractors found with the provided names: " + contractorNames);
            System.out.println("Available contractor names: " +
                    contractorRepository.findAll().stream()
                            .map(ContractorEntity::getVendorName)
                            .collect(Collectors.toList()));
            throw new IllegalArgumentException("No contractors found with the provided names: " + contractorNames);
        }

        // Update basic information
        entity.setYardNumber(dto.getYardNumber());
        entity.setProjectName(dto.getProjectName());

        // Update contractorName (client name) if provided
        if (dto.getContractorName() != null && !dto.getContractorName().trim().isEmpty()) {
            entity.setContractorName(dto.getContractorName());
            System.out.println("Client name (contractorName) updated to: " + dto.getContractorName());
        }

        entity.setContractors(contractors);

        // Update rejection reason if provided and PR is in REJECTED status
        if ("REJECTED".equals(entity.getStatus()) && dto.getRejectionReason() != null) {
            entity.setRejectionReason(dto.getRejectionReason());
            System.out.println("Updated rejection reason for PR " + entity.getId() + ": " + dto.getRejectionReason());
        }

        // Status will be set after checking for quantity changes

        // Update line items
        if (dto.getLineItems() != null) {
            // Validate for duplicate line items before updating
            validateNoDuplicateLineItems(dto.getLineItems());

            List<PurchaseRequestItemEntity> updatedLineItems = dto.getLineItems().stream()
                    .map(itemDTO -> {
                        // Find existing item or create new one
                        PurchaseRequestItemEntity item = entity.getLineItems().stream()
                                .filter(existingItem -> existingItem.getId().equals(itemDTO.getId()))
                                .findFirst()
                                .orElse(new PurchaseRequestItemEntity());

                        // Update item properties
                        item.setUniqueCode(itemDTO.getUniqueCode());
                        item.setProductName(itemDTO.getProductName());
                        item.setUnitOfMeasure(itemDTO.getUnitOfMeasure());
                        item.setRate(itemDTO.getRate());
                        item.setItemName(itemDTO.getItemName());
                        item.setMaterialFamily(getMaterialFamilyByUniqueCode(itemDTO.getUniqueCode()));

                        // Handle quantity changes only for REGULAR PRs
                        if (entity.getPrType().equals("REGULAR")) {
                            if (itemDTO.getChangeInQuantity() != null && itemDTO.getChangeInQuantity() != 0) {
                                item.setQuantity(itemDTO.getQuantity());
                                item.setChangeInQuantity(itemDTO.getChangeInQuantity());
                                item.setQuantityUpdated(true);
                            } else {
                                item.setQuantity(itemDTO.getQuantity());
                                item.setChangeInQuantity(0);
                                item.setQuantityUpdated(false);
                            }
                        } else {
                            // For instant PRs, set these to null
                            item.setQuantity(itemDTO.getQuantity());
                            item.setChangeInQuantity(null);
                            item.setQuantityUpdated(null);
                        }

                        // Calculate total based on quantity
                        item.setTotal(item.getRate().multiply(BigDecimal.valueOf(itemDTO.getQuantity())));

                        item.setPurchaseRequest(entity);
                        return item;
                    })
                    .collect(Collectors.toList());

            // Clear existing items and add updated ones
            entity.getLineItems().clear();
            entity.getLineItems().addAll(updatedLineItems);

            // Check if any line item has a change in quantity only for REGULAR PRs
            if (entity.getPrType().equals("REGULAR")) {
                boolean hasQuantityChanges = updatedLineItems.stream()
                        .anyMatch(item -> item.getChangeInQuantity() != null && item.getChangeInQuantity() != 0);
                entity.setQuantityUpdated(hasQuantityChanges);

                // Set status based on quantity changes
                if (requestedStatus != null && !requestedStatus.isEmpty()) {
                    // If status is explicitly set in the DTO, respect that
                    entity.setStatus(requestedStatus);
                } else if (hasQuantityChanges) {
                    // If quantity changed, set to PENDING_QUANTITY_APPROVAL
                    entity.setStatus("PENDING_QUANTITY_APPROVAL");
                } else {
                    // If no quantity changes, set to PENDING_APPROVAL first
                    entity.setStatus("PENDING_APPROVAL");
                }
                System.out.println("PR " + entity.getId() + " status set to " + entity.getStatus() +
                                 " (hasQuantityChanges=" + hasQuantityChanges + ")");
            }
        } else {
            // If no line items provided, set quantityUpdated based on PR type
            entity.setQuantityUpdated(entity.getPrType().equals("REGULAR") ? false : null);

            // Respect explicitly requested status or default to PENDING_APPROVAL
            if (requestedStatus != null && !requestedStatus.isEmpty()) {
                entity.setStatus(requestedStatus);
                System.out.println("PR " + entity.getId() + " (no line items) status explicitly set to " + requestedStatus);
            } else {
                entity.setStatus("PENDING_APPROVAL");
                System.out.println("PR " + entity.getId() + " (no line items) status set to PENDING_APPROVAL");
            }
        }

        // In updatePurchaseRequest, after handling line items and before saving entity
        // Calculate grand total for instant PRs if line items were updated
        if (entity.getPrType().equals("INSTANT") && dto.getLineItems() != null) {
            BigDecimal grandTotal = entity.getLineItems().stream()
                    .map(PurchaseRequestItemEntity::getTotal)
                    .filter(total -> total != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            entity.setGrandTotal(grandTotal);
            System.out.println("Updated grand total for instant PR: " + grandTotal);
        }

        // Set status based on PR type if not already set
        if (entity.getPrType().equals("INSTANT")) {
            entity.setStatus("IN_PROGRESS_MANAGEMENT");
        }

        PurchaseRequestEntity updatedEntity = purchaseRequestRepository.save(entity);

        // Send email to each vendor with configurable URL - only for REGULAR PRs
        // This is needed when vendors are added to a PR that was created from an MR
        // Skip sending emails if the PR is in PENDING_QUANTITY_APPROVAL status (quantity changes)
        // Only send emails if this is a new PR or if vendors have been changed
        if (!"INSTANT".equals(entity.getPrType()) &&
            !"PENDING_QUANTITY_APPROVAL".equals(entity.getStatus()) &&
            (originalStatus.equals("PENDING_VENDOR_SELECTION") ||
             !entity.getVendors().equals(updatedEntity.getVendors()))) {

            String baseFormUrl = frontendUrl + biddingFormPath;
            System.out.println("Sending bidding links to vendors for PR ID: " + updatedEntity.getId());
            for (VendorEntity vendor : vendors) {
                try {
                    // Generate unique token for this vendor and PR
                    String token = tokenService.generateBiddingToken(updatedEntity, vendor);
                    String formUrl = baseFormUrl + "?token=" + token;

                    System.out.println("Sending bidding link to vendor: " + vendor.getCompanyName() + " (" + vendor.getEmailId() + ")");
                    System.out.println("Bidding link with token: " + formUrl);

                    emailService.sendQuotationRequestEmail(
                            vendor.getEmailId(),
                            vendor.getCompanyName(),
                            updatedEntity.getId(),
                            formUrl
                    );
                    System.out.println("Email sent successfully to vendor: " + vendor.getCompanyName());
                } catch (Exception e) {
                    System.err.println("Error sending email to vendor " + vendor.getCompanyName() + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            if ("PENDING_QUANTITY_APPROVAL".equals(entity.getStatus())) {
                System.out.println("Skipping sending bidding links for PR ID: " + updatedEntity.getId() + " because it's in PENDING_QUANTITY_APPROVAL status");
            } else if ("INSTANT".equals(entity.getPrType())) {
                System.out.println("Skipping sending bidding links for INSTANT PR ID: " + updatedEntity.getId());
            } else {
                System.out.println("Skipping sending bidding links for PR ID: " + updatedEntity.getId() + " - No vendor changes detected");
            }
        }

        return convertToDTO(updatedEntity);
    }

    @Transactional
    public void deletePurchaseRequest(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Check if PR can be deleted
        if (pr.getStatus().equals("APPROVED")) {
            throw new IllegalStateException("Cannot delete an approved Purchase Request");
        }

        // First delete all related vendor quotations
        List<VendorQuotationEntity> quotations = vendorQuotationRepository.findByPurchaseRequestId(prId);
        if (!quotations.isEmpty()) {
            System.out.println("Deleting " + quotations.size() + " related quotations for PR " + prId);
            vendorQuotationRepository.deleteAll(quotations);
        }

        // Now delete the purchase request
        purchaseRequestRepository.delete(pr);
    }

    @Transactional
    public PurchaseRequestDTO sendForApproval(Long prId) {
        PurchaseRequestEntity entity = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in a valid state for approval
        if (!"PENDING_VENDOR_SELECTION".equals(entity.getStatus()) && !"DRAFT".equals(entity.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in PENDING_VENDOR_SELECTION or DRAFT state to send for approval");
        }

        // Check if any line items have quantity changes only for REGULAR PRs
        boolean hasQuantityChanges = entity.getPrType().equals("REGULAR") && entity.getLineItems().stream()
                .anyMatch(item -> item.getChangeInQuantity() != null && item.getChangeInQuantity() != 0);

        // Update status based on quantity changes and PR type
        if (entity.getPrType().equals("REGULAR") && hasQuantityChanges) {
            entity.setStatus("PENDING_QUANTITY_APPROVAL");
        } else {
            entity.setStatus("PENDING_APPROVAL");
        }

        // Save the updated entity
        PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);
        return convertToDTO(savedEntity);
    }

    /**
     * Send a purchase request from internal validation back to vendors with updated quantities
     * This is used when there are quantity changes that need to be communicated back to vendors
     * After sending emails, the PR status is set to PENDING_APPROVAL so it appears in the RFQ dashboard
     * and is removed from the internal section
     *
     * When quantities are changed, existing quotations are deleted to ensure vendors submit new prices
     * based on the updated quantities
     *
     * IMPORTANT: This method should ONLY be called when quantities have been changed by the engineer department
     * and need to be communicated back to vendors. Do NOT call this method when quantities are not changed.
     */
    @Transactional
    public PurchaseRequestDTO sendBackToVendors(Long prId) {
        PurchaseRequestEntity entity = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in a valid state
        if (!"PENDING_QUANTITY_APPROVAL".equals(entity.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in PENDING_QUANTITY_APPROVAL state to send back to vendors");
        }

        // Verify this is a REGULAR PR with quantity changes
        if (!"REGULAR".equals(entity.getPrType()) || !entity.getQuantityUpdated()) {
            throw new IllegalStateException("Only REGULAR purchase requests with quantity changes can be sent back to vendors");
        }

        // Keep existing quotations for partial quantity change behavior
        // Vendors will be able to see their previous quotation data for unchanged items
        List<VendorQuotationEntity> existingQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);
        if (!existingQuotations.isEmpty()) {
            System.out.println("Preserving " + existingQuotations.size() + " existing quotations for PR " + prId + " to support partial quantity changes");
        }

        // Update status to PENDING_APPROVAL so it appears in the RFQ dashboard
        entity.setStatus("PENDING_APPROVAL");
        System.out.println("PR " + entity.getId() + " status set to PENDING_APPROVAL after sending updated quantities to vendors");



        // Save the updated entity
        PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);

        // Send email to each vendor with updated quantities
        String baseFormUrl = frontendUrl + biddingFormPath;
        for (VendorEntity vendor : savedEntity.getVendors()) {
            try {
                // Invalidate old tokens and generate new one for quantity changes
                tokenService.invalidateUnusedTokensForPRAndVendor(savedEntity.getId(), vendor.getSrNo());
                String token = tokenService.generateBiddingToken(savedEntity, vendor);
                String formUrl = baseFormUrl + "?token=" + token;

                // Use the new email method for quantity changes
                emailService.sendQuantityChangedEmail(
                        vendor.getEmailId(),
                        vendor.getCompanyName(),
                        savedEntity.getId(),
                        formUrl
                );
                System.out.println("Sent quantity change email with new token to vendor: " + vendor.getCompanyName());
            } catch (Exception e) {
                System.err.println("Error sending quantity change email to vendor " + vendor.getCompanyName() + ": " + e.getMessage());
                e.printStackTrace();
            }
        }

        return convertToDTO(savedEntity);
    }

    @Transactional
    public PurchaseRequestDTO approveRejectPurchaseRequest(Long prId, String action, String rejectionReason) {
        PurchaseRequestEntity entity = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));

        if ("APPROVE".equals(action)) {
            if ("PENDING_APPROVAL".equals(entity.getStatus())) {
                // No quantity changes, move to IN_PROGRESS_MANAGEMENT
                entity.setStatus("IN_PROGRESS_MANAGEMENT");
                entity.setQuantityUpdated(false);
                PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);
                return convertToDTO(savedEntity);
            } else if ("REJECTED".equals(entity.getStatus())) {
                // When re-approving a rejected PR, move it directly to APPROVED since management has already reviewed it
                entity.setStatus("APPROVED");
                entity.setApprovalDate(new Date());
                entity.setRejectionReason(null); // Clear the rejection reason
                PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);
                return convertToDTO(savedEntity);
            }
            if ("PENDING_QUANTITY_APPROVAL".equals(entity.getStatus())) {
                // Check for ACTUAL quantity changes
                boolean hasQuantityChanges = false;
                if (entity.getPrType().equals("REGULAR")) {
                    hasQuantityChanges = entity.getLineItems().stream()
                        .anyMatch(item ->
                            item.getQuantityUpdated() != null &&
                            item.getQuantityUpdated() &&
                            item.getChangeInQuantity() != null &&
                            item.getChangeInQuantity() != 0);

                    System.out.println("PR " + entity.getId() + " quantity changes check: " + hasQuantityChanges);
                }

                if (hasQuantityChanges) {
                    // If there are quantity changes, move to PENDING_APPROVAL and send emails
                    entity.setStatus("PENDING_APPROVAL");
                    entity.setQuantityUpdated(true);

                    PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);

                    // Send emails to vendors about quantity changes
                    String baseFormUrl = frontendUrl + biddingFormPath;
                    for (VendorEntity vendor : savedEntity.getVendors()) {
                        try {
                            // Get the total change in quantity for this PR
                            int totalChangeInQuantity = savedEntity.getLineItems().stream()
                                .filter(item -> item.getChangeInQuantity() != null)
                                .mapToInt(item -> item.getChangeInQuantity())
                                .sum();

                            // Only send email if there are actual quantity changes
                            if (totalChangeInQuantity != 0) {
                                // Invalidate old tokens and generate new one for quantity changes
                                tokenService.invalidateUnusedTokensForPRAndVendor(savedEntity.getId(), vendor.getSrNo());
                                String token = tokenService.generateBiddingToken(savedEntity, vendor);
                                String formUrl = baseFormUrl + "?token=" + token;

                                emailService.sendQuantityChangedEmail(
                                vendor.getEmailId(),
                                vendor.getCompanyName(),
                                savedEntity.getId(),
                                    formUrl
                                );
                                System.out.println("Sent quantity change email with new token to " + vendor.getCompanyName() + " for PR#" + savedEntity.getId());
                            } else {
                                System.out.println("Skipping email to " + vendor.getCompanyName() + " - No actual quantity changes");
                            }
                        } catch (Exception e) {
                            System.err.println("Error sending quantity change email to vendor " + vendor.getCompanyName() + ": " + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                    return convertToDTO(savedEntity);
                } else {
                    // If no quantity changes, move directly to Management WITHOUT sending emails
                    entity.setStatus("IN_PROGRESS_MANAGEMENT");
                    entity.setQuantityUpdated(false);
                    System.out.println("No quantity changes detected - moving PR " + entity.getId() +
                        " directly to IN_PROGRESS_MANAGEMENT without sending emails");
                    PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);
                    return convertToDTO(savedEntity);
                }
            } else if ("IN_PROGRESS_MANAGEMENT".equals(entity.getStatus())) {
                // Handle management approval
                entity.setStatus("APPROVED");
                entity.setApprovalDate(new Date());
                PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);
                return convertToDTO(savedEntity);
            }
        } else if ("REJECT".equals(action)) {
            if (rejectionReason == null || rejectionReason.trim().isEmpty()) {
                throw new IllegalArgumentException("Rejection reason is required when rejecting a purchase request");
            }
            entity.setStatus("REJECTED");
            entity.setRejectionReason(rejectionReason);
        PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(entity);
        return convertToDTO(savedEntity);
        }

        throw new IllegalStateException("Invalid action or status for purchase request");
    }

    private String generatePRId() {
        String datePrefix = "PR-" + java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE) + "-";

        // Find the maximum sequence number for today's date
        int maxSequence = 0;
        List<PurchaseRequestEntity> existingPRs = purchaseRequestRepository.findAll();
        for (PurchaseRequestEntity pr : existingPRs) {
            if (pr.getPrId() != null && pr.getPrId().startsWith(datePrefix)) {
                try {
                    String sequenceStr = pr.getPrId().substring(datePrefix.length());
                    int sequence = Integer.parseInt(sequenceStr);
                    maxSequence = Math.max(maxSequence, sequence);
                } catch (NumberFormatException | IndexOutOfBoundsException e) {
                    // Skip if not a valid format
                }
            }
        }

        // Generate a new ID with the next sequence number
        return datePrefix + String.format("%04d", maxSequence + 1);
    }

    private void validatePurchaseRequest(PurchaseRequestDTO dto) {
        if (dto.getYardNumber() == null || dto.getYardNumber().trim().isEmpty()) {
            throw new IllegalArgumentException("Yard number is required");
        }
        if (dto.getProjectName() == null || dto.getProjectName().trim().isEmpty()) {
            throw new IllegalArgumentException("Project name is required");
        }
        if (dto.getLineItems() == null || dto.getLineItems().isEmpty()) {
            throw new IllegalArgumentException("At least one line item is required");
        }
        if (dto.getContractorNames() == null || dto.getContractorNames().isEmpty()) {
            throw new IllegalArgumentException("At least one contractor is required");
        }
        if (dto.getVendorNames() == null || dto.getVendorNames().isEmpty()) {
            throw new IllegalArgumentException("At least one vendor is required");
        }
        if (dto.getPrType() == null || (!dto.getPrType().equals("REGULAR") && !dto.getPrType().equals("INSTANT"))) {
            throw new IllegalArgumentException("PR type must be either REGULAR or INSTANT");
        }
        if (dto.getPrType().equals("REGULAR") && dto.getContractorNames().size() == 0) {
            throw new IllegalArgumentException("Regular PR must have at least 1 contractor");
        }
        if (dto.getPrType().equals("INSTANT") && dto.getContractorNames().size() == 0) {
            throw new IllegalArgumentException("Instant PR must have at least 1 contractor");
        }
    }

    /**
     * Full conversion of entity to DTO with all related entities
     */
    private PurchaseRequestDTO convertToDTO(PurchaseRequestEntity entity) {
        PurchaseRequestDTO dto = new PurchaseRequestDTO();
        dto.setId(entity.getId());
        dto.setPrId(entity.getPrId());

        // If this PR is linked to an MR, get data from there
        if (entity.getMaterialRequisition() != null) {
            MaterialRequisitionEntity mr = entity.getMaterialRequisition();
            dto.setYardNumber(mr.getYardNumber());
            dto.setProjectName(mr.getProjectName());
            dto.setMaterialRequisitionId(mr.getId());
            // Set contractorName (client name) from MR
            dto.setContractorName(mr.getContractorName());
        } else {
            dto.setYardNumber(entity.getYardNumber());
            dto.setProjectName(entity.getProjectName());
            // Set contractorName (client name) from PR entity
            if (entity.getContractorName() != null && !entity.getContractorName().trim().isEmpty()) {
                dto.setContractorName(entity.getContractorName());
            }
        }

        // Set contractor names from ContractorEntity list
        dto.setContractorNames(entity.getContractors().stream()
                .map(ContractorEntity::getVendorName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        dto.setCreatedDate(entity.getCreatedDate());
        dto.setStatus(entity.getStatus());
        dto.setPrType(entity.getPrType());
        dto.setRejectionReason(entity.getRejectionReason());
        dto.setApprovalDate(entity.getApprovalDate());
        dto.setRevisedGrandTotal(entity.getRevisedGrandTotal());
        dto.setGrandTotal(entity.getGrandTotal());
        dto.setRemarks(entity.getRemarks());

        // Set quantityUpdated only for REGULAR PRs
        if (entity.getPrType().equals("REGULAR")) {
            dto.setQuantityUpdated(entity.getQuantityUpdated() != null ? entity.getQuantityUpdated() : false);
        }

        // Set isReshared flag
        dto.setIsReshared(entity.getIsReshared() != null ? entity.getIsReshared() : false);

        dto.setVendorNames(entity.getVendors().stream()
                .map(VendorEntity::getCompanyName)
                .collect(Collectors.toList()));

        if (entity.getLineItems() != null) {
            dto.setLineItems(entity.getLineItems().stream()
                    .map(item -> {
                        PurchaseRequestItemDTO itemDTO = new PurchaseRequestItemDTO();
                        itemDTO.setId(item.getId());
                        itemDTO.setUniqueCode(item.getUniqueCode());
                        itemDTO.setProductName(item.getProductName());
                        itemDTO.setUnitOfMeasure(item.getUnitOfMeasure());
                        itemDTO.setQuantity(item.getQuantity());
                        itemDTO.setRate(item.getRate());
                        itemDTO.setTotal(item.getTotal());
                        itemDTO.setItemName(item.getItemName());
                        itemDTO.setMaterialFamily(item.getMaterialFamily());

                        // Only set quantity-related fields for REGULAR PRs
                        if (entity.getPrType().equals("REGULAR")) {
                            itemDTO.setChangeInQuantity(item.getChangeInQuantity());
                            itemDTO.setQuantityUpdated(item.getQuantityUpdated());
                        }

                        return itemDTO;
                    })
                    .collect(Collectors.toList()));

            dto.setLineItemCount(entity.getLineItems().size());
        }

        return dto;
    }

    public List<PurchaseRequestLightDTO> getPurchaseRequestsApprovedLight() {
        List<Object[]> nativeResults = purchaseRequestRepository.findLightDTOsByStatusWithVendorsNative("APPROVED");
        return PurchaseRequestMapper.mapToPurchaseRequestLightDTOs(nativeResults);
    }

    /**
     * Get vendors for a specific Purchase Request with their RFQ response status
     * @param prId Purchase Request ID
     * @return List of vendors with their response status
     */
    public List<VendorRfqStatusDTO> getVendorsForPR(Long prId) {
        // Get the purchase request with vendors
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Get all quotations for this PR to check which vendors have responded
        List<VendorQuotationEntity> quotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

        // Create a set of vendor IDs who have submitted quotations
        Set<Long> respondedVendorIds = quotations.stream()
                .map(q -> q.getVendor().getSrNo())
                .collect(Collectors.toSet());

        // Map vendors to VendorRfqStatusDTO
        return pr.getVendors().stream()
                .map(vendor -> {
                    boolean hasResponded = respondedVendorIds.contains(vendor.getSrNo());
                    return new VendorRfqStatusDTO(
                            vendor.getSrNo(),
                            vendor.getCompanyName(),
                            vendor.getVendorName(),
                            vendor.getEmailId(),
                            vendor.getContactNumber(),
                            hasResponded,
                            hasResponded ? "Responded" : "Not Responded"
                    );
                })
                .collect(Collectors.toList());
    }

    /**
     * Resend RFQ emails to selected vendors
     * @param prId Purchase Request ID
     * @param vendorIds List of vendor IDs to send emails to
     * @return Number of emails sent successfully
     */
    @Transactional
    public int resendRfqEmails(Long prId, List<Long> vendorIds) {
        // Get the purchase request
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in a valid state for resending emails
        if (!"PENDING_APPROVAL".equals(pr.getStatus())) {
            throw new IllegalStateException("Cannot resend RFQ emails for PR with status: " + pr.getStatus());
        }

        // Get vendors from the PR's vendor list that match the provided IDs
        List<VendorEntity> vendorsToEmail = pr.getVendors().stream()
                .filter(vendor -> vendorIds.contains(vendor.getSrNo()))
                .collect(Collectors.toList());

        if (vendorsToEmail.isEmpty()) {
            throw new IllegalArgumentException("No valid vendors found for the provided vendor IDs");
        }

        int emailsSent = 0;
        String baseFormUrl = frontendUrl + biddingFormPath;

        System.out.println("Resending RFQ emails for PR ID: " + prId + " to " + vendorsToEmail.size() + " vendors");

        for (VendorEntity vendor : vendorsToEmail) {
            try {
                // Generate new token for resending (this makes the link valid for one more response)
                // Set isReshared=true to track that this token was generated from a reshare operation
                String token = tokenService.generateBiddingToken(pr, vendor, true);
                String formUrl = baseFormUrl + "?token=" + token;

                System.out.println("Resending RFQ email to vendor: " + vendor.getCompanyName() + " (" + vendor.getEmailId() + ")");
                System.out.println("Bidding link with new token: " + formUrl);

                emailService.sendQuotationRequestEmail(
                        vendor.getEmailId(),
                        vendor.getCompanyName(),
                        prId,
                        formUrl
                );
                emailsSent++;
                System.out.println("RFQ email resent successfully with new token to: " + vendor.getCompanyName());
            } catch (Exception e) {
                System.err.println("Failed to send RFQ email to vendor: " + vendor.getCompanyName() + " - " + e.getMessage());
                e.printStackTrace();
                // Continue with other vendors even if one fails
            }
        }

        System.out.println("Successfully resent " + emailsSent + " out of " + vendorsToEmail.size() + " RFQ emails for PR " + prId);

        // Set isReshared flag to true if any emails were sent successfully
        if (emailsSent > 0) {
            pr.setIsReshared(true);
            purchaseRequestRepository.save(pr);
            System.out.println("Set isReshared flag to true for PR " + prId);
        }

        return emailsSent;
    }

    /**
     * Get approved PO details with line items and grand total for management approval
     * @param prId Purchase Request ID
     * @return ApprovedPODTO with line items and calculated grand total
     */
    public ApprovedPODTO getApprovedPODetails(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to view approved PO details");
        }

        ApprovedPODTO approvedPO = new ApprovedPODTO();
        approvedPO.setPrId(pr.getId());
        approvedPO.setPrIdString(pr.getPrId());
        approvedPO.setYardNumber(pr.getYardNumber());
        approvedPO.setProjectName(pr.getProjectName());
        approvedPO.setContractorName(pr.getContractorName());
        approvedPO.setStatus(pr.getStatus());
        approvedPO.setRevisedGrandTotal(pr.getRevisedGrandTotal());

        // Get selected quotation items for this PR
        Map<String, VendorQuotationItemEntity> selectedQuotationItems = getSelectedQuotationItemsMap(prId);

        // Convert line items to approved PO line items using quotation data when available
        List<ApprovedPOLineItemDTO> lineItems = new ArrayList<>();
        BigDecimal grandTotal = BigDecimal.ZERO;

        if (pr.getLineItems() != null) {
            int itemCode = 1;
            for (PurchaseRequestItemEntity item : pr.getLineItems()) {
                ApprovedPOLineItemDTO lineItem = new ApprovedPOLineItemDTO();
                lineItem.setUniqueCode(item.getUniqueCode());
                lineItem.setItemCode(String.format("%02d", itemCode++));
                lineItem.setDescription(item.getProductName());
                lineItem.setMaterialFamily(item.getMaterialFamily());
                lineItem.setUnitOfMeasure(item.getUnitOfMeasure());

                // Check if there's a selected quotation for this item
                VendorQuotationItemEntity selectedQuotation = selectedQuotationItems.get(item.getUniqueCode());

                BigDecimal itemTotal;
                if (selectedQuotation != null) {
                    // Use quotation data (quantity, rate, total)
                    lineItem.setQuantity(selectedQuotation.getAvailableQuantity());
                    lineItem.setRateApproved(BigDecimal.valueOf(selectedQuotation.getUnitPrice()));
                    itemTotal = BigDecimal.valueOf(selectedQuotation.getAvailableQuantity() * selectedQuotation.getUnitPrice());
                    lineItem.setTotal(itemTotal);

                    System.out.println("DEBUG: Using quotation data for " + item.getUniqueCode() +
                                     " - Qty: " + selectedQuotation.getAvailableQuantity() +
                                     ", Rate: " + selectedQuotation.getUnitPrice() +
                                     ", Total: " + itemTotal);
                } else {
                    // Fallback to original PR data
                    lineItem.setQuantity(item.getQuantity());
                    lineItem.setRateApproved(item.getRate() != null ? item.getRate() : BigDecimal.ZERO);

                    // Calculate total for this line item
                    itemTotal = item.getTotal();
                    if (itemTotal == null && item.getRate() != null && item.getQuantity() != null) {
                        itemTotal = item.getRate().multiply(new BigDecimal(item.getQuantity()));
                    }
                    if (itemTotal == null) {
                        itemTotal = BigDecimal.ZERO;
                    }
                    lineItem.setTotal(itemTotal);

                    System.out.println("DEBUG: Using PR data for " + item.getUniqueCode() +
                                     " - Qty: " + item.getQuantity() +
                                     ", Rate: " + lineItem.getRateApproved() +
                                     ", Total: " + itemTotal);
                }

                lineItems.add(lineItem);
                grandTotal = grandTotal.add(itemTotal);
            }
        }

        approvedPO.setLineItems(lineItems);
        approvedPO.setGrandTotal(grandTotal);

        return approvedPO;
    }

    /**
     * Revise payment amount for a purchase request
     * @param prId Purchase Request ID
     * @param paymentRevision Payment revision details
     * @return Updated ApprovedPODTO
     */
    @Transactional
    public ApprovedPODTO revisePayment(Long prId, PaymentRevisionDTO paymentRevision) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to revise payment");
        }

        // Validate the new grand total
        if (paymentRevision.getNewGrandTotal() == null || paymentRevision.getNewGrandTotal().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("New grand total must be greater than zero");
        }

        // Update the revised grand total
        pr.setRevisedGrandTotal(paymentRevision.getNewGrandTotal());
        purchaseRequestRepository.save(pr);

        System.out.println("Payment revised for PR " + prId + " to " + paymentRevision.getNewGrandTotal());

        // Return updated approved PO details
        return getApprovedPODetails(prId);
    }

    /**
     * Get approved PO details for approved dashboard
     * This method is specifically for viewing already approved POs
     * @param prId Purchase Request ID
     * @return ApprovedPODTO with line items and grand total for approved POs
     */
    public ApprovedPODTO getApprovedPODetailsForDashboard(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in APPROVED status
        if (!"APPROVED".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in APPROVED status to view approved PO dashboard details");
        }

        ApprovedPODTO approvedPO = new ApprovedPODTO();
        approvedPO.setPrId(pr.getId());
        approvedPO.setPrIdString(pr.getPrId());
        approvedPO.setYardNumber(pr.getYardNumber());
        approvedPO.setProjectName(pr.getProjectName());
        approvedPO.setContractorName(pr.getContractorName());
        approvedPO.setStatus(pr.getStatus());
        approvedPO.setRevisedGrandTotal(pr.getRevisedGrandTotal());

        // Get selected quotation items for this PR
        Map<String, VendorQuotationItemEntity> selectedQuotationItems = getSelectedQuotationItemsMap(prId);

        // Convert line items to approved PO line items using quotation data when available
        List<ApprovedPOLineItemDTO> lineItems = new ArrayList<>();
        BigDecimal grandTotal = BigDecimal.ZERO;

        if (pr.getLineItems() != null) {
            int itemCode = 1;
            for (PurchaseRequestItemEntity item : pr.getLineItems()) {
                ApprovedPOLineItemDTO lineItem = new ApprovedPOLineItemDTO();
                lineItem.setUniqueCode(item.getUniqueCode());
                lineItem.setItemCode(String.format("%02d", itemCode++));
                lineItem.setDescription(item.getProductName());
                lineItem.setMaterialFamily(item.getMaterialFamily());
                lineItem.setUnitOfMeasure(item.getUnitOfMeasure());

                // Check if there's a selected quotation for this item
                VendorQuotationItemEntity selectedQuotation = selectedQuotationItems.get(item.getUniqueCode());

                BigDecimal itemTotal;
                if (selectedQuotation != null) {
                    // Use quotation data (quantity, rate, total)
                    lineItem.setQuantity(selectedQuotation.getAvailableQuantity());
                    lineItem.setRateApproved(BigDecimal.valueOf(selectedQuotation.getUnitPrice()));
                    itemTotal = BigDecimal.valueOf(selectedQuotation.getAvailableQuantity() * selectedQuotation.getUnitPrice());
                    lineItem.setTotal(itemTotal);

                    System.out.println("DEBUG: Using quotation data for approved PO " + item.getUniqueCode() +
                                     " - Qty: " + selectedQuotation.getAvailableQuantity() +
                                     ", Rate: " + selectedQuotation.getUnitPrice() +
                                     ", Total: " + itemTotal);
                } else {
                    // Fallback to original PR data
                    lineItem.setQuantity(item.getQuantity());
                    lineItem.setRateApproved(item.getRate() != null ? item.getRate() : BigDecimal.ZERO);

                    // Calculate total for this line item
                    itemTotal = item.getTotal();
                    if (itemTotal == null && item.getRate() != null && item.getQuantity() != null) {
                        itemTotal = item.getRate().multiply(new BigDecimal(item.getQuantity()));
                    }
                    if (itemTotal == null) {
                        itemTotal = BigDecimal.ZERO;
                    }
                    lineItem.setTotal(itemTotal);

                    System.out.println("DEBUG: Using PR data for approved PO " + item.getUniqueCode() +
                                     " - Qty: " + item.getQuantity() +
                                     ", Rate: " + lineItem.getRateApproved() +
                                     ", Total: " + itemTotal);
                }

                lineItems.add(lineItem);
                grandTotal = grandTotal.add(itemTotal);
            }
        }

        approvedPO.setLineItems(lineItems);
        approvedPO.setGrandTotal(grandTotal);

        return approvedPO;
    }

    /**
     * Get vendor-split PO details for management tab
     * This method splits a PR into vendor-specific POs for individual management approval
     * @param prId Purchase Request ID
     * @return List of VendorSplitPODTO - one for each vendor with their selected items
     */
    public List<VendorSplitPODTO> getVendorSplitPODetails(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to view vendor-split PO details");
        }

        // Get all quotations for this PR
        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

        if (allQuotations.isEmpty()) {
            throw new IllegalStateException("No quotations found for PR: " + prId);
        }

        // Get selected quotation items grouped by vendor
        Map<VendorEntity, List<VendorQuotationItemEntity>> vendorItemsMap = allQuotations.stream()
            .flatMap(quotation -> quotation.getItems().stream()
                .filter(item -> Boolean.TRUE.equals(item.getSelected()))
                .map(item -> Map.entry(quotation.getVendor(), item)))
            .collect(Collectors.groupingBy(
                Map.Entry::getKey,
                Collectors.mapping(Map.Entry::getValue, Collectors.toList())
            ));

        if (vendorItemsMap.isEmpty()) {
            throw new IllegalStateException("No selected quotation items found for PR: " + prId);
        }

        List<VendorSplitPODTO> vendorPOs = new ArrayList<>();
        int vendorSequence = 1;
        int totalVendors = vendorItemsMap.size();

        for (Map.Entry<VendorEntity, List<VendorQuotationItemEntity>> entry : vendorItemsMap.entrySet()) {
            VendorEntity vendor = entry.getKey();
            List<VendorQuotationItemEntity> selectedItems = entry.getValue();

            VendorSplitPODTO vendorPO = new VendorSplitPODTO();

            // Set basic PR info
            vendorPO.setOriginalPrId(pr.getId());
            vendorPO.setOriginalPrIdString(pr.getPrId());
            // Generate vendorPoId: if only one vendor, use original PR ID, otherwise append sequence
            if (totalVendors == 1) {
                vendorPO.setVendorPoId(pr.getId().toString());
            } else {
                vendorPO.setVendorPoId(pr.getId() + "-" + vendorSequence);
            }
            vendorPO.setYardNumber(pr.getYardNumber());
            vendorPO.setProjectName(pr.getProjectName());
            vendorPO.setContractorName(pr.getContractorName());
            vendorPO.setCreatedDate(pr.getCreatedDate());
            vendorPO.setStatus(pr.getStatus());

            // Set vendor info
            vendorPO.setVendorId(vendor.getSrNo());
            vendorPO.setVendorCompanyName(vendor.getCompanyName());
            vendorPO.setVendorName(vendor.getVendorName());
            vendorPO.setVendorEmail(vendor.getEmailId());
            vendorPO.setVendorContactNumber(vendor.getContactNumber());
            vendorPO.setVendorAddress(vendor.getAddress());

            // Convert selected items to line items
            List<ApprovedPOLineItemDTO> lineItems = new ArrayList<>();
            BigDecimal vendorGrandTotal = BigDecimal.ZERO;
            int itemCode = 1;

            for (VendorQuotationItemEntity selectedItem : selectedItems) {
                ApprovedPOLineItemDTO lineItem = new ApprovedPOLineItemDTO();
                lineItem.setUniqueCode(selectedItem.getUniqueCode());
                lineItem.setItemCode(String.format("%02d", itemCode++));
                lineItem.setDescription(selectedItem.getProductName());
                lineItem.setMaterialFamily(getMaterialFamilyByUniqueCode(selectedItem.getUniqueCode()));
                lineItem.setQuantity(selectedItem.getAvailableQuantity());
                lineItem.setUnitOfMeasure("pcs"); // Default unit, can be enhanced later
                lineItem.setRateApproved(BigDecimal.valueOf(selectedItem.getUnitPrice()));

                BigDecimal itemTotal = BigDecimal.valueOf(selectedItem.getAvailableQuantity() * selectedItem.getUnitPrice());
                lineItem.setTotal(itemTotal);

                lineItems.add(lineItem);
                vendorGrandTotal = vendorGrandTotal.add(itemTotal);
            }

            vendorPO.setLineItems(lineItems);
            vendorPO.setVendorGrandTotal(vendorGrandTotal);
            vendorPO.setLineItemCount(lineItems.size());

            vendorPOs.add(vendorPO);
            vendorSequence++;
        }

        System.out.println("Generated " + vendorPOs.size() + " vendor-specific POs for PR " + prId);
        return vendorPOs;
    }

    /**
     * Revise payment for a specific vendor in vendor-split workflow
     * @param prId Purchase Request ID
     * @param vendorId Vendor ID to revise payment for
     * @param newVendorTotal New grand total for this specific vendor
     * @return Updated VendorSplitPODTO for the specific vendor
     */
    public VendorSplitPODTO reviseVendorPayment(Long prId, Long vendorId, BigDecimal newVendorTotal) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to revise vendor payment");
        }

        // Get all vendor-split POs
        List<VendorSplitPODTO> vendorPOs = getVendorSplitPODetails(prId);

        // Find the specific vendor PO
        VendorSplitPODTO targetVendorPO = vendorPOs.stream()
                .filter(vendorPO -> vendorPO.getVendorId().equals(vendorId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Vendor not found in PR " + prId + " with vendor ID: " + vendorId));

        // Update the revised grand total for this vendor
        targetVendorPO.setVendorRevisedGrandTotal(newVendorTotal);

        // Persist the payment revision to database
        VendorPaymentRevisionEntity existingRevision = vendorPaymentRevisionRepository
                .findByPurchaseRequestIdAndVendorId(prId, vendorId)
                .orElse(null);

        if (existingRevision != null) {
            // Update existing revision
            existingRevision.setRevisedAmount(newVendorTotal);
            existingRevision.setRevisionDate(new Date());
            vendorPaymentRevisionRepository.save(existingRevision);
        } else {
            // Create new revision record
            VendorPaymentRevisionEntity newRevision = new VendorPaymentRevisionEntity(
                    prId, vendorId, targetVendorPO.getVendorGrandTotal(),
                    newVendorTotal, targetVendorPO.getVendorPoId()
            );
            vendorPaymentRevisionRepository.save(newRevision);
        }

        // Log the payment revision
        System.out.println("Payment revised for Vendor PO " + targetVendorPO.getVendorPoId() +
                          " - Original: " + targetVendorPO.getVendorGrandTotal() +
                          ", Revised: " + newVendorTotal + " (persisted to database)");

        return targetVendorPO;
    }

    /**
     * Approve a specific vendor in vendor-split workflow
     * @param prId Purchase Request ID
     * @param vendorId Vendor ID to approve
     * @param revisedTotal Optional revised total for this vendor
     * @return Updated VendorSplitPODTO for the approved vendor
     */
    public VendorSplitPODTO approveVendor(Long prId, Long vendorId, BigDecimal revisedTotal) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to approve vendor");
        }

        // Get the vendor PO details
        List<VendorSplitPODTO> vendorPOs = getVendorSplitPODetails(prId);
        VendorSplitPODTO targetVendorPO = vendorPOs.stream()
                .filter(vendorPO -> vendorPO.getVendorId().equals(vendorId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Vendor not found in PR " + prId + " with vendor ID: " + vendorId));

        // Apply revised total if provided
        if (revisedTotal != null) {
            targetVendorPO.setVendorRevisedGrandTotal(revisedTotal);
        }

        // Mark this vendor as approved (you can add a status field to VendorSplitPODTO if needed)
        System.out.println("Vendor PO " + targetVendorPO.getVendorPoId() + " approved" +
                          (revisedTotal != null ? " with revised total: " + revisedTotal : ""));

        return targetVendorPO;
    }

    /**
     * Submit approved PO for final approval (with or without payment revision)
     * @param prId Purchase Request ID
     * @param finalGrandTotal Final grand total amount (optional - uses existing if not provided)
     * @return Updated PurchaseRequestDTO
     */
    @Transactional
    public PurchaseRequestDTO submitApprovedPO(Long prId, BigDecimal finalGrandTotal) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to submit for approval");
        }

        // If final grand total is provided, update the revised grand total
        if (finalGrandTotal != null && finalGrandTotal.compareTo(BigDecimal.ZERO) > 0) {
            pr.setRevisedGrandTotal(finalGrandTotal);
        }

        // Move to APPROVED status
        pr.setStatus("APPROVED");
        pr.setApprovalDate(new Date());

        PurchaseRequestEntity savedEntity = purchaseRequestRepository.save(pr);

        System.out.println("Purchase Request " + prId + " approved with final grand total: " +
                          (pr.getRevisedGrandTotal() != null ? pr.getRevisedGrandTotal() : "original amount"));

        return convertToDTO(savedEntity);
    }

    /**
     * Update PR line items with rates from selected quotations
     * This should be called when quotations are selected to populate rates
     */
    @Transactional
    public void updatePRItemsWithSelectedQuotationRates(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Get all quotations for this PR
        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

        if (allQuotations.isEmpty()) {
            System.out.println("WARNING: No quotations found for PR " + prId);
            return;
        }

        // Get all selected quotation items
        List<VendorQuotationItemEntity> selectedItems = allQuotations.stream()
            .flatMap(quotation -> quotation.getItems().stream())
            .filter(item -> Boolean.TRUE.equals(item.getSelected()))
            .collect(Collectors.toList());

        if (selectedItems.isEmpty()) {
            System.out.println("WARNING: No selected quotation items found for PR " + prId);
            return;
        }

        System.out.println("DEBUG: Found " + selectedItems.size() + " selected quotation items for PR " + prId);

        // Update PR items with rates from selected quotation items
        for (PurchaseRequestItemEntity prItem : pr.getLineItems()) {
            // Find matching selected quotation item by uniqueCode
            selectedItems.stream()
                .filter(quotationItem -> quotationItem.getUniqueCode().equals(prItem.getUniqueCode()))
                .findFirst()
                .ifPresent(selectedItem -> {
                    // Update rate and total
                    BigDecimal rate = BigDecimal.valueOf(selectedItem.getUnitPrice());
                    prItem.setRate(rate);

                    // Calculate total (quantity * rate)
                    BigDecimal quantity = new BigDecimal(prItem.getQuantity());
                    BigDecimal total = quantity.multiply(rate);
                    prItem.setTotal(total);

                    System.out.println("DEBUG: Updated PR item " + prItem.getProductName() +
                                      " with rate " + rate + " from selected quotation");
                });
        }

        // Save the updated PR
        purchaseRequestRepository.save(pr);
        System.out.println("DEBUG: Updated PR " + prId + " with selected quotation rates");
    }

    /**
     * Get selected quotation items mapped by uniqueCode for approved PO calculations
     */
    private Map<String, VendorQuotationItemEntity> getSelectedQuotationItemsMap(Long prId) {
        // Get all quotations for this PR
        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

        // Get all selected quotation items and map by uniqueCode
        return allQuotations.stream()
            .flatMap(quotation -> quotation.getItems().stream())
            .filter(item -> Boolean.TRUE.equals(item.getSelected()))
            .collect(Collectors.toMap(
                VendorQuotationItemEntity::getUniqueCode,
                item -> item,
                (existing, replacement) -> existing // Keep first if duplicates
            ));
    }

    /**
     * Submit all vendors for approval - creates separate approved POs for each vendor
     * @param prId Purchase Request ID
     * @param vendorRevisions Map of vendorId -> revised total (optional)
     * @return List of approved vendor POs with their individual status and revised payments
     */
    public List<VendorSplitPODTO> submitAllVendorsForApproval(Long prId, Map<Long, BigDecimal> vendorRevisions) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in IN_PROGRESS_MANAGEMENT status
        if (!"IN_PROGRESS_MANAGEMENT".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in IN_PROGRESS_MANAGEMENT status to submit all vendors");
        }

        // Get all vendor-split POs
        List<VendorSplitPODTO> vendorPOs = getVendorSplitPODetails(prId);

        if (vendorPOs.isEmpty()) {
            throw new IllegalStateException("No vendor POs found for PR: " + prId);
        }

        // Process each vendor PO
        List<VendorSplitPODTO> approvedVendorPOs = new ArrayList<>();

        for (VendorSplitPODTO vendorPO : vendorPOs) {
            // Apply revised payment if provided for this vendor
            if (vendorRevisions != null && vendorRevisions.containsKey(vendorPO.getVendorId())) {
                BigDecimal revisedTotal = vendorRevisions.get(vendorPO.getVendorId());
                vendorPO.setVendorRevisedGrandTotal(revisedTotal);
                System.out.println("Applied revised payment for " + vendorPO.getVendorPoId() +
                                 ": " + revisedTotal);
            }

            // Mark vendor PO as approved
            vendorPO.setStatus("APPROVED");
            approvedVendorPOs.add(vendorPO);

            System.out.println("Vendor PO " + vendorPO.getVendorPoId() + " approved with total: " +
                             (vendorPO.getVendorRevisedGrandTotal() != null ?
                              vendorPO.getVendorRevisedGrandTotal() : vendorPO.getVendorGrandTotal()));
        }

        // Update the main PR status to APPROVED
        pr.setStatus("APPROVED");
        pr.setApprovalDate(new Date());
        purchaseRequestRepository.save(pr);

        System.out.println("All vendors approved for PR " + prId + ". Created " +
                          approvedVendorPOs.size() + " separate vendor POs");

        return approvedVendorPOs;
    }

    /**
     * Get all approved vendor-split POs for PO submission dashboard
     * @return List of all approved vendor POs from all PRs
     */
    public List<VendorSplitPODTO> getAllApprovedVendorPOs() {
        // Get all approved PRs
        List<PurchaseRequestEntity> approvedPRs = purchaseRequestRepository.findByStatusIgnoreCase("APPROVED");

        List<VendorSplitPODTO> allApprovedVendorPOs = new ArrayList<>();

        for (PurchaseRequestEntity pr : approvedPRs) {
            try {
                // Get vendor-split details for each approved PR
                List<VendorSplitPODTO> vendorPOs = getVendorSplitPODetailsForApproved(pr.getId());

                // Mark all as approved status
                for (VendorSplitPODTO vendorPO : vendorPOs) {
                    vendorPO.setStatus("APPROVED");
                }

                allApprovedVendorPOs.addAll(vendorPOs);
            } catch (Exception e) {
                // Skip PRs that don't have vendor quotations (might be single-vendor PRs)
                System.out.println("Skipping PR " + pr.getId() + " - no vendor quotations found: " + e.getMessage());
            }
        }

        System.out.println("Found " + allApprovedVendorPOs.size() + " approved vendor POs from " + approvedPRs.size() + " approved PRs");
        return allApprovedVendorPOs;
    }

    /**
     * Get all approved vendor-split POs for gate pass creation (REGULAR PRs only)
     * @return List of all approved vendor POs from REGULAR PRs only
     */
    public List<VendorSplitPODTO> getAllApprovedVendorPOsForGatePass() {
        // Get all approved PRs
        List<PurchaseRequestEntity> approvedPRs = purchaseRequestRepository.findByStatusIgnoreCase("APPROVED");

        // Filter to only include REGULAR PRs (exclude INSTANT PRs)
        List<PurchaseRequestEntity> regularApprovedPRs = approvedPRs.stream()
                .filter(pr -> "REGULAR".equals(pr.getPrType()))
                .collect(Collectors.toList());

        List<VendorSplitPODTO> allApprovedVendorPOs = new ArrayList<>();

        for (PurchaseRequestEntity pr : regularApprovedPRs) {
            try {
                // Get vendor-split details for each approved PR
                List<VendorSplitPODTO> vendorPOs = getVendorSplitPODetailsForApproved(pr.getId());

                // Mark all as approved status
                for (VendorSplitPODTO vendorPO : vendorPOs) {
                    vendorPO.setStatus("APPROVED");
                }

                allApprovedVendorPOs.addAll(vendorPOs);
            } catch (Exception e) {
                // Skip PRs that don't have vendor quotations (might be single-vendor PRs)
                System.out.println("Skipping PR " + pr.getId() + " - no vendor quotations found: " + e.getMessage());
            }
        }

        System.out.println("Found " + allApprovedVendorPOs.size() + " approved vendor POs from " + regularApprovedPRs.size() + " regular approved PRs");
        return allApprovedVendorPOs;
    }

    /**
     * Get vendor-split PO details for approved PRs (similar to getVendorSplitPODetails but for approved status)
     */
    private List<VendorSplitPODTO> getVendorSplitPODetailsForApproved(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Get all quotations for this PR
        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

        if (allQuotations.isEmpty()) {
            // Handle instant PRs that don't have quotations
            System.out.println("No quotations found for PR " + prId + " (likely an instant PR). Using PR line items and vendors directly.");
            return getVendorSplitPODetailsForInstantPR(pr);
        }

        // Get selected quotation items grouped by vendor
        Map<VendorEntity, List<VendorQuotationItemEntity>> vendorItemsMap = allQuotations.stream()
            .flatMap(quotation -> quotation.getItems().stream()
                .filter(item -> Boolean.TRUE.equals(item.getSelected()))
                .map(item -> Map.entry(quotation.getVendor(), item)))
            .collect(Collectors.groupingBy(
                Map.Entry::getKey,
                Collectors.mapping(Map.Entry::getValue, Collectors.toList())
            ));

        if (vendorItemsMap.isEmpty()) {
            throw new IllegalStateException("No selected quotation items found for PR: " + prId);
        }

        List<VendorSplitPODTO> vendorPOs = new ArrayList<>();
        int vendorSequence = 1;
        int totalVendors = vendorItemsMap.size();

        for (Map.Entry<VendorEntity, List<VendorQuotationItemEntity>> entry : vendorItemsMap.entrySet()) {
            VendorEntity vendor = entry.getKey();
            List<VendorQuotationItemEntity> selectedItems = entry.getValue();

            VendorSplitPODTO vendorPO = new VendorSplitPODTO();

            // Set basic PR info
            vendorPO.setOriginalPrId(pr.getId());
            vendorPO.setOriginalPrIdString(pr.getPrId());
            // Generate vendorPoId: if only one vendor, use original PR ID, otherwise append sequence
            if (totalVendors == 1) {
                vendorPO.setVendorPoId(pr.getId().toString());
            } else {
                vendorPO.setVendorPoId(pr.getId() + "-" + vendorSequence);
            }
            vendorPO.setYardNumber(pr.getYardNumber());
            vendorPO.setProjectName(pr.getProjectName());
            vendorPO.setContractorName(pr.getContractorName());
            vendorPO.setCreatedDate(pr.getCreatedDate());
            vendorPO.setStatus("APPROVED");

            // Set vendor info
            vendorPO.setVendorId(vendor.getSrNo());
            vendorPO.setVendorCompanyName(vendor.getCompanyName());
            vendorPO.setVendorName(vendor.getVendorName());
            vendorPO.setVendorEmail(vendor.getEmailId());
            vendorPO.setVendorContactNumber(vendor.getContactNumber());
            vendorPO.setVendorAddress(vendor.getAddress());

            // Convert selected items to line items
            List<ApprovedPOLineItemDTO> lineItems = new ArrayList<>();
            BigDecimal vendorGrandTotal = BigDecimal.ZERO;
            int itemCode = 1;

            for (VendorQuotationItemEntity selectedItem : selectedItems) {
                ApprovedPOLineItemDTO lineItem = new ApprovedPOLineItemDTO();
                lineItem.setUniqueCode(selectedItem.getUniqueCode());
                lineItem.setItemCode(String.format("%02d", itemCode++));
                lineItem.setDescription(selectedItem.getProductName());
                lineItem.setMaterialFamily(getMaterialFamilyByUniqueCode(selectedItem.getUniqueCode()));
                lineItem.setQuantity(selectedItem.getAvailableQuantity());
                lineItem.setUnitOfMeasure("pcs");
                lineItem.setRateApproved(BigDecimal.valueOf(selectedItem.getUnitPrice()));

                BigDecimal itemTotal = BigDecimal.valueOf(selectedItem.getAvailableQuantity() * selectedItem.getUnitPrice());
                lineItem.setTotal(itemTotal);

                lineItems.add(lineItem);
                vendorGrandTotal = vendorGrandTotal.add(itemTotal);
            }

            vendorPO.setLineItems(lineItems);
            vendorPO.setVendorGrandTotal(vendorGrandTotal);
            vendorPO.setLineItemCount(lineItems.size());

            // Load persisted payment revision if exists
            vendorPaymentRevisionRepository.findByPurchaseRequestIdAndVendorId(prId, vendor.getSrNo())
                    .ifPresent(revision -> {
                        vendorPO.setVendorRevisedGrandTotal(revision.getRevisedAmount());
                        System.out.println("Loaded revised payment for " + vendorPO.getVendorPoId() +
                                         ": " + revision.getRevisedAmount());
                    });

            vendorPOs.add(vendorPO);
            vendorSequence++;
        }

        return vendorPOs;
    }

    /**
     * Get vendor-split PO details for instant PRs that don't have quotations
     * Uses PR line items and vendors directly
     */
    private List<VendorSplitPODTO> getVendorSplitPODetailsForInstantPR(PurchaseRequestEntity pr) {
        List<VendorSplitPODTO> vendorPOs = new ArrayList<>();

        // For instant PRs, we typically have one vendor or need to create a single PO
        // Check if PR has vendors associated
        if (pr.getVendors() == null || pr.getVendors().isEmpty()) {
            // If no vendors, create a single PO with contractor as vendor
            VendorSplitPODTO vendorPO = new VendorSplitPODTO();

            // Set basic PR info
            vendorPO.setOriginalPrId(pr.getId());
            vendorPO.setOriginalPrIdString(pr.getPrId());
            // For instant PRs with no vendors (contractor as vendor), use original PR ID without suffix
            vendorPO.setVendorPoId(pr.getId().toString());
            vendorPO.setYardNumber(pr.getYardNumber());
            vendorPO.setProjectName(pr.getProjectName());
            vendorPO.setContractorName(pr.getContractorName());
            vendorPO.setCreatedDate(pr.getCreatedDate());
            vendorPO.setStatus("APPROVED");

            // Use contractor as vendor info (for instant PRs without specific vendors)
            vendorPO.setVendorId(0L); // Default vendor ID
            vendorPO.setVendorCompanyName(pr.getContractorName());
            vendorPO.setVendorName(pr.getContractorName());
            vendorPO.setVendorEmail(""); // No email for contractor
            vendorPO.setVendorContactNumber(""); // No contact for contractor

            // Convert PR line items to vendor PO line items
            List<ApprovedPOLineItemDTO> lineItems = convertPRLineItemsToApprovedPOLineItems(pr.getLineItems());
            vendorPO.setLineItems(lineItems);

            // Calculate grand total from line items
            BigDecimal vendorGrandTotal = lineItems.stream()
                    .map(ApprovedPOLineItemDTO::getTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vendorPO.setVendorGrandTotal(vendorGrandTotal);
            vendorPO.setLineItemCount(lineItems.size());

            // Check for payment revision
            vendorPaymentRevisionRepository.findByPurchaseRequestIdAndVendorId(pr.getId(), 0L)
                    .ifPresent(revision -> {
                        vendorPO.setVendorRevisedGrandTotal(revision.getRevisedAmount());
                        System.out.println("Loaded revised payment for instant PR " + vendorPO.getVendorPoId() +
                                         ": " + revision.getRevisedAmount());
                    });

            vendorPOs.add(vendorPO);
        } else {
            // If PR has vendors, create separate POs for each vendor
            int vendorSequence = 1;
            int totalVendors = pr.getVendors().size();
            for (VendorEntity vendor : pr.getVendors()) {
                VendorSplitPODTO vendorPO = new VendorSplitPODTO();

                // Set basic PR info
                vendorPO.setOriginalPrId(pr.getId());
                vendorPO.setOriginalPrIdString(pr.getPrId());
                // Generate vendorPoId: if only one vendor (typical for instant PRs), use original PR ID, otherwise append sequence
                if (totalVendors == 1) {
                    vendorPO.setVendorPoId(pr.getId().toString());
                } else {
                    vendorPO.setVendorPoId(pr.getId() + "-" + vendorSequence);
                }
                vendorPO.setYardNumber(pr.getYardNumber());
                vendorPO.setProjectName(pr.getProjectName());
                vendorPO.setContractorName(pr.getContractorName());
                vendorPO.setCreatedDate(pr.getCreatedDate());
                vendorPO.setStatus("APPROVED");

                // Set vendor info
                vendorPO.setVendorId(vendor.getSrNo());
                vendorPO.setVendorCompanyName(vendor.getCompanyName());
                vendorPO.setVendorName(vendor.getVendorName());
                vendorPO.setVendorEmail(vendor.getEmailId());
                vendorPO.setVendorContactNumber(vendor.getContactNumber());
                vendorPO.setVendorAddress(vendor.getAddress());

                // For instant PRs, all line items go to each vendor (or split logic can be added later)
                List<ApprovedPOLineItemDTO> lineItems = convertPRLineItemsToApprovedPOLineItems(pr.getLineItems());
                vendorPO.setLineItems(lineItems);

                // Calculate grand total from line items
                BigDecimal vendorGrandTotal = lineItems.stream()
                        .map(ApprovedPOLineItemDTO::getTotal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vendorPO.setVendorGrandTotal(vendorGrandTotal);
                vendorPO.setLineItemCount(lineItems.size());

                // Check for payment revision
                vendorPaymentRevisionRepository.findByPurchaseRequestIdAndVendorId(pr.getId(), vendor.getSrNo())
                        .ifPresent(revision -> {
                            vendorPO.setVendorRevisedGrandTotal(revision.getRevisedAmount());
                            System.out.println("Loaded revised payment for instant PR " + vendorPO.getVendorPoId() +
                                             ": " + revision.getRevisedAmount());
                        });

                vendorPOs.add(vendorPO);
                vendorSequence++;
            }
        }

        System.out.println("Created " + vendorPOs.size() + " vendor POs for instant PR " + pr.getId());
        return vendorPOs;
    }

    /**
     * Convert PR line items to approved PO line items
     */
    private List<ApprovedPOLineItemDTO> convertPRLineItemsToApprovedPOLineItems(List<PurchaseRequestItemEntity> prLineItems) {
        List<ApprovedPOLineItemDTO> lineItems = new ArrayList<>();

        if (prLineItems != null) {
            int itemCode = 1;
            for (PurchaseRequestItemEntity prItem : prLineItems) {
                ApprovedPOLineItemDTO lineItem = new ApprovedPOLineItemDTO();
                lineItem.setUniqueCode(prItem.getUniqueCode());
                lineItem.setItemCode(String.format("%02d", itemCode++));
                lineItem.setDescription(prItem.getProductName());
                lineItem.setMaterialFamily(prItem.getMaterialFamily());
                lineItem.setQuantity(prItem.getQuantity());
                lineItem.setUnitOfMeasure(prItem.getUnitOfMeasure());
                lineItem.setRateApproved(prItem.getRate());
                lineItem.setTotal(prItem.getTotal());

                lineItems.add(lineItem);
            }
        }

        return lineItems;
    }

    /**
     * Get vendor-split PO details for APPROVED PR (same structure as management view)
     * @param prId Purchase Request ID
     * @return List of vendor POs with their individual data and revised payments
     */
    public List<VendorSplitPODTO> getApprovedVendorSplitPODetails(Long prId) {
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

        // Validate that the PR is in APPROVED status
        if (!"APPROVED".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request must be in APPROVED status. Current status: " + pr.getStatus());
        }

        // Use the same method as management view but for approved status
        return getVendorSplitPODetailsForApproved(prId);
    }

    /**
     * BATCH VERSION: Get vendor-split PO details for multiple APPROVED PRs
     * This method processes multiple PRs in batch to improve performance
     * @param prIds List of Purchase Request IDs
     * @return Map of PR ID to List of vendor POs
     */
    public Map<Long, List<VendorSplitPODTO>> getBatchApprovedVendorSplitPODetails(List<Long> prIds) {
        if (prIds == null || prIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, List<VendorSplitPODTO>> result = new HashMap<>();

        // Get all PRs in batch
        List<PurchaseRequestEntity> prs = purchaseRequestRepository.findAllById(prIds);

        // Filter only approved PRs
        List<PurchaseRequestEntity> approvedPRs = prs.stream()
                .filter(pr -> "APPROVED".equals(pr.getStatus()))
                .collect(Collectors.toList());

        if (approvedPRs.isEmpty()) {
            return result;
        }

        // Get all quotations for all PRs in batch
        List<Long> approvedPrIds = approvedPRs.stream()
                .map(PurchaseRequestEntity::getId)
                .collect(Collectors.toList());

        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestIdIn(approvedPrIds);

        // Group quotations by PR ID
        Map<Long, List<VendorQuotationEntity>> quotationsByPrId = allQuotations.stream()
                .collect(Collectors.groupingBy(q -> q.getPurchaseRequest().getId()));

        // Process each approved PR
        for (PurchaseRequestEntity pr : approvedPRs) {
            try {
                List<VendorQuotationEntity> prQuotations = quotationsByPrId.getOrDefault(pr.getId(), new ArrayList<>());

                if (prQuotations.isEmpty()) {
                    // Handle instant PRs that don't have quotations
                    result.put(pr.getId(), getVendorSplitPODetailsForInstantPR(pr));
                } else {
                    // Process regular PRs with quotations
                    result.put(pr.getId(), processVendorSplitPOForPR(pr, prQuotations));
                }
            } catch (Exception e) {
                System.out.println("Error processing PR " + pr.getId() + " in batch: " + e.getMessage());
                result.put(pr.getId(), new ArrayList<>());
            }
        }

        return result;
    }

    /**
     * Helper method to process vendor split PO for a single PR with its quotations
     * Extracted from getVendorSplitPODetailsForApproved for batch processing
     */
    private List<VendorSplitPODTO> processVendorSplitPOForPR(PurchaseRequestEntity pr, List<VendorQuotationEntity> quotations) {
        // Get selected quotation items grouped by vendor
        Map<VendorEntity, List<VendorQuotationItemEntity>> vendorItemsMap = quotations.stream()
            .flatMap(quotation -> quotation.getItems().stream()
                .filter(item -> Boolean.TRUE.equals(item.getSelected()))
                .map(item -> Map.entry(quotation.getVendor(), item)))
            .collect(Collectors.groupingBy(
                Map.Entry::getKey,
                Collectors.mapping(Map.Entry::getValue, Collectors.toList())
            ));

        if (vendorItemsMap.isEmpty()) {
            return new ArrayList<>();
        }

        List<VendorSplitPODTO> vendorPOs = new ArrayList<>();
        int vendorSequence = 1;
        int totalVendors = vendorItemsMap.size();

        for (Map.Entry<VendorEntity, List<VendorQuotationItemEntity>> entry : vendorItemsMap.entrySet()) {
            VendorEntity vendor = entry.getKey();
            List<VendorQuotationItemEntity> selectedItems = entry.getValue();

            VendorSplitPODTO vendorPO = new VendorSplitPODTO();

            // Set basic PR info
            vendorPO.setOriginalPrId(pr.getId());
            vendorPO.setOriginalPrIdString(pr.getPrId());
            // Generate vendorPoId: if only one vendor, use original PR ID, otherwise append sequence
            if (totalVendors == 1) {
                vendorPO.setVendorPoId(pr.getId().toString());
            } else {
                vendorPO.setVendorPoId(pr.getId() + "-" + vendorSequence);
            }
            vendorPO.setYardNumber(pr.getYardNumber());
            vendorPO.setProjectName(pr.getProjectName());
            vendorPO.setContractorName(pr.getContractorName());
            vendorPO.setCreatedDate(pr.getCreatedDate());
            vendorPO.setStatus("APPROVED");

            // Set vendor info
            vendorPO.setVendorId(vendor.getSrNo());
            vendorPO.setVendorCompanyName(vendor.getCompanyName());
            vendorPO.setVendorName(vendor.getVendorName());
            vendorPO.setVendorEmail(vendor.getEmailId());
            vendorPO.setVendorContactNumber(vendor.getContactNumber());
            vendorPO.setVendorAddress(vendor.getAddress());

            // Convert selected items to line items
            List<ApprovedPOLineItemDTO> lineItems = new ArrayList<>();
            BigDecimal vendorGrandTotal = BigDecimal.ZERO;
            int itemCode = 1;

            for (VendorQuotationItemEntity selectedItem : selectedItems) {
                ApprovedPOLineItemDTO lineItem = new ApprovedPOLineItemDTO();
                lineItem.setUniqueCode(selectedItem.getUniqueCode());
                lineItem.setItemCode(String.format("%02d", itemCode++));
                lineItem.setDescription(selectedItem.getProductName());
                lineItem.setMaterialFamily(getMaterialFamilyByUniqueCode(selectedItem.getUniqueCode()));
                lineItem.setQuantity(selectedItem.getAvailableQuantity());
                lineItem.setUnitOfMeasure("pcs");
                lineItem.setRateApproved(BigDecimal.valueOf(selectedItem.getUnitPrice()));

                BigDecimal itemTotal = BigDecimal.valueOf(selectedItem.getAvailableQuantity() * selectedItem.getUnitPrice());
                lineItem.setTotal(itemTotal);

                lineItems.add(lineItem);
                vendorGrandTotal = vendorGrandTotal.add(itemTotal);
            }

            vendorPO.setLineItems(lineItems);
            vendorPO.setVendorGrandTotal(vendorGrandTotal);
            vendorPO.setLineItemCount(lineItems.size());

            // Load persisted payment revision if exists
            vendorPaymentRevisionRepository.findByPurchaseRequestIdAndVendorId(pr.getId(), vendor.getSrNo())
                    .ifPresent(revision -> vendorPO.setVendorRevisedGrandTotal(revision.getRevisedAmount()));

            vendorPOs.add(vendorPO);
            vendorSequence++;
        }

        return vendorPOs;
    }

    /**
     * Get a specific vendor PO by vendor PO ID efficiently (without loading all vendor POs)
     * @param vendorPoId Vendor PO ID (e.g., "647-1")
     * @return VendorSplitPODTO for the specific vendor PO
     */
    public VendorSplitPODTO getVendorPOByVendorPoId(String vendorPoId) {
        System.out.println("Looking for vendor PO ID: " + vendorPoId);

        // Parse vendor PO ID to extract PR ID
        // Handle both formats: single vendor (just PR ID) and multi-vendor (PR ID with suffix)
        String[] parts = vendorPoId.split("-");
        Long prId;

        try {
            if (parts.length == 1) {
                // Single vendor format: just PR ID (e.g., "621")
                prId = Long.parseLong(parts[0]);
                System.out.println("Parsed single vendor PO - PR ID: " + prId);
            } else if (parts.length == 2) {
                // Multi-vendor format: PR ID with sequence (e.g., "621-1")
                prId = Long.parseLong(parts[0]);
                int vendorSequence = Integer.parseInt(parts[1]);
                System.out.println("Parsed multi-vendor PO - PR ID: " + prId + ", Vendor Sequence: " + vendorSequence);
            } else {
                throw new IllegalArgumentException("Invalid vendor PO ID format. Expected format: 'prId' or 'prId-vendorSequence'. Got: " + vendorPoId);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid vendor PO ID format. Expected format: 'prId' or 'prId-vendorSequence'. Got: " + vendorPoId);
        }

        // Get the specific PR
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with database id: " + prId + " (from vendor PO ID: " + vendorPoId + ")"));

        System.out.println("Found PR with ID: " + pr.getId() + ", PR String: " + pr.getPrId() + ", Status: " + pr.getStatus());

        // Validate that the PR is in APPROVED status
        if (!"APPROVED".equals(pr.getStatus())) {
            throw new IllegalStateException("Purchase Request " + prId + " must be in APPROVED status. Current status: " + pr.getStatus());
        }

        // Get vendor-split details for this specific PR
        try {
            List<VendorSplitPODTO> vendorPOs = getVendorSplitPODetailsForApproved(prId);
            System.out.println("Found " + vendorPOs.size() + " vendor POs for PR " + prId);

            for (VendorSplitPODTO po : vendorPOs) {
                System.out.println("Available vendor PO ID: " + po.getVendorPoId() + " for vendor: " + po.getVendorCompanyName());
            }

            // Find the specific vendor PO by vendor sequence
            return vendorPOs.stream()
                    .filter(po -> po.getVendorPoId().equals(vendorPoId))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Vendor PO not found with ID: " + vendorPoId +
                        ". Available vendor PO IDs: " + vendorPOs.stream().map(VendorSplitPODTO::getVendorPoId).collect(Collectors.toList())));
        } catch (Exception e) {
            System.out.println("Error getting vendor split details for PR " + prId + ": " + e.getMessage());
            throw new IllegalStateException("Failed to get vendor split details for PR " + prId + ": " + e.getMessage());
        }
    }

    /**
     * Adjusts a date to the start of the day (00:00:00.000)
     * This ensures that date filtering includes all records from the beginning of the day
     */
    private Date adjustToStartOfDay(Date date) {
        if (date == null) {
            return null;
        }

        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0);
        calendar.set(java.util.Calendar.MINUTE, 0);
        calendar.set(java.util.Calendar.SECOND, 0);
        calendar.set(java.util.Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * Adjusts a date to the end of the day (23:59:59.999)
     * This ensures that date filtering includes all records until the end of the day
     */
    private Date adjustToEndOfDay(Date date) {
        if (date == null) {
            return null;
        }

        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 23);
        calendar.set(java.util.Calendar.MINUTE, 59);
        calendar.set(java.util.Calendar.SECOND, 59);
        calendar.set(java.util.Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    /**
     * Validates that there are no duplicate line items in the purchase request
     * Items are considered duplicates if they have the same uniqueCode or the same productName + unitOfMeasure combination
     */
    private void validateNoDuplicateLineItems(List<PurchaseRequestItemDTO> lineItems) {
        if (lineItems == null || lineItems.isEmpty()) {
            return;
        }

        Set<String> seenItems = new HashSet<>();

        for (PurchaseRequestItemDTO item : lineItems) {
            String itemKey = generateItemKey(item.getUniqueCode(), item.getProductName(), item.getUnitOfMeasure());

            if (seenItems.contains(itemKey)) {
                String itemDescription = item.getProductName() != null ? item.getProductName() :
                                       (item.getUniqueCode() != null ? item.getUniqueCode() : "Unknown item");
                throw new IllegalArgumentException(
                    "Duplicate line item detected: " + itemDescription +
                    ". Please remove duplicate items before submitting."
                );
            }
            seenItems.add(itemKey);
        }
    }

    /**
     * Generates a unique key for an item based on uniqueCode or productName + unitOfMeasure
     */
    private String generateItemKey(String uniqueCode, String productName, String unitOfMeasure) {
        // Primary key: uniqueCode (if available and not empty)
        if (uniqueCode != null && !uniqueCode.trim().isEmpty()) {
            return "CODE:" + uniqueCode.toLowerCase().trim();
        }

        // Fallback key: productName + unitOfMeasure combination
        String name = (productName != null ? productName.trim() : "");
        String unit = (unitOfMeasure != null ? unitOfMeasure.trim() : "");

        if (name.isEmpty()) {
            throw new IllegalArgumentException("Item must have either uniqueCode or productName");
        }

        return "NAME_UNIT:" + (name + "|" + unit).toLowerCase();
    }

    /**
     * Helper method to get material family by unique code from ShipbuildersItemEntity
     */
    private String getMaterialFamilyByUniqueCode(String uniqueCode) {
        if (uniqueCode == null || uniqueCode.trim().isEmpty()) {
            System.out.println("DEBUG: uniqueCode is null or empty");
            return null;
        }

        System.out.println("DEBUG: Looking up material family for uniqueCode: " + uniqueCode);

        Optional<ShipbuildersItemEntity> itemOpt = shipbuildersItemRepository.findByItemCode(uniqueCode);
        if (itemOpt.isPresent()) {
            String materialFamily = itemOpt.get().getSubCategory();
            System.out.println("DEBUG: Found material family '" + materialFamily + "' for uniqueCode: " + uniqueCode);
            return materialFamily;
        } else {
            System.out.println("DEBUG: No shipbuilders item found for uniqueCode: " + uniqueCode);
            return null;
        }
    }

}

