package com.synergy.service;

import com.synergy.dto.BillDTO;
import com.synergy.dto.GRNFormDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.dto.ApprovedPOLineItemDTO;
import com.synergy.entity.BillEntity;
import com.synergy.entity.BillLineItemEntity;
import com.synergy.entity.GRNEntity;
import com.synergy.repository.BillRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class BillService {

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    /**
     * Create bill from GRN
     */
    public BillEntity createBillFromGRN(GRNEntity grn, List<GRNFormDTO.GRNLineItemDTO> billItems) {
        // Check if bill already exists for this GRN
        if (billRepository.existsByGrnId(grn.getId())) {
            throw new IllegalArgumentException("Bill already exists for GRN: " + grn.getGrnNumber());
        }

        // Get vendor PO details for pricing information
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(grn.getVendorPoId());

        // Create bill entity
        BillEntity bill = new BillEntity();
        bill.setBillNumber(generateBillNumber(grn.getVendorPoId()));
        bill.setGrnId(grn.getId());
        bill.setGrnNumber(grn.getGrnNumber());
        bill.setVendorPoId(grn.getVendorPoId());
        bill.setYardNumber(grn.getYardNumber());
        bill.setProjectName(grn.getProjectName());
        bill.setVendorCompanyName(grn.getVendorCompanyName());
        bill.setStatus("DRAFT");
        bill.setRemarks("Bill created from GRN: " + grn.getGrnNumber());

        // Create line items for bill
        List<BillLineItemEntity> lineItems = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (GRNFormDTO.GRNLineItemDTO grnItem : billItems) {
            // Find corresponding PO line item for pricing
            ApprovedPOLineItemDTO poItem = findPOLineItem(vendorPO, grnItem.getUniqueCode());
            
            BillLineItemEntity billItem = new BillLineItemEntity();
            billItem.setBill(bill);
            billItem.setItemCode(grnItem.getItemCode());
            billItem.setDescription(grnItem.getDescription());
            billItem.setMaterialFamily(grnItem.getMaterialFamily());
            billItem.setQtyOrdered(grnItem.getQtyOrdered());
            billItem.setQtyReceived(grnItem.getQtyReceived());
            billItem.setStatus(grnItem.getStatus());
            billItem.setLocation(grnItem.getLocation());
            billItem.setRemarks(grnItem.getRemarks());
            billItem.setQualityCheckRequirement(grnItem.getQualityCheckRequirement());
            billItem.setUniqueCode(grnItem.getUniqueCode());

            // Set pricing information
            if (poItem != null && poItem.getRateApproved() != null) {
                billItem.setUnitPrice(poItem.getRateApproved());
                
                // Calculate total amount based on received quantity
                // For missing/damage items, received qty is 0, so amount will be 0
                Integer qtyForBilling = grnItem.getQtyReceived() != null ? grnItem.getQtyReceived() : 0;
                BigDecimal itemTotal = poItem.getRateApproved().multiply(BigDecimal.valueOf(qtyForBilling));
                billItem.setTotalAmount(itemTotal);
                totalAmount = totalAmount.add(itemTotal);
            } else {
                billItem.setUnitPrice(BigDecimal.ZERO);
                billItem.setTotalAmount(BigDecimal.ZERO);
            }

            lineItems.add(billItem);
        }

        bill.setLineItems(lineItems);
        bill.setTotalAmount(totalAmount);

        return billRepository.save(bill);
    }

    /**
     * Get all bills
     */
    public List<BillDTO> getAllBills() {
        List<BillEntity> bills = billRepository.findAllByOrderByCreatedDateDesc();
        return bills.stream().map(this::convertToDTO).toList();
    }

    /**
     * Get bills by status
     */
    public List<BillDTO> getBillsByStatus(String status) {
        List<BillEntity> bills = billRepository.findByStatusIgnoreCaseOrderByCreatedDateDesc(status);
        return bills.stream().map(this::convertToDTO).toList();
    }

    /**
     * Get bill by ID
     */
    public BillDTO getBillById(Long id) {
        Optional<BillEntity> billOpt = billRepository.findById(id);
        if (billOpt.isEmpty()) {
            throw new IllegalArgumentException("Bill not found with ID: " + id);
        }
        return convertToDTO(billOpt.get());
    }

    /**
     * Find PO line item by unique code
     */
    private ApprovedPOLineItemDTO findPOLineItem(VendorSplitPODTO vendorPO, String uniqueCode) {
        if (vendorPO.getLineItems() != null) {
            return vendorPO.getLineItems().stream()
                    .filter(item -> uniqueCode.equals(item.getUniqueCode()))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    /**
     * Convert entity to DTO
     */
    private BillDTO convertToDTO(BillEntity bill) {
        BillDTO dto = new BillDTO();
        dto.setId(bill.getId());
        dto.setBillNumber(bill.getBillNumber());
        dto.setGrnId(bill.getGrnId());
        dto.setGrnNumber(bill.getGrnNumber());
        dto.setVendorPoId(bill.getVendorPoId());
        dto.setYardNumber(bill.getYardNumber());
        dto.setProjectName(bill.getProjectName());
        dto.setVendorCompanyName(bill.getVendorCompanyName());
        dto.setTotalAmount(bill.getTotalAmount());
        dto.setStatus(bill.getStatus());
        dto.setCreatedDate(bill.getCreatedDate());
        dto.setUpdatedDate(bill.getUpdatedDate());
        dto.setApprovedDate(bill.getApprovedDate());
        dto.setPaidDate(bill.getPaidDate());
        dto.setRemarks(bill.getRemarks());

        // Set line item count
        dto.setLineItemCount(bill.getLineItems() != null ? bill.getLineItems().size() : 0);

        // Convert line items
        if (bill.getLineItems() != null) {
            List<BillDTO.BillLineItemDTO> lineItemDTOs = new ArrayList<>();
            for (BillLineItemEntity item : bill.getLineItems()) {
                BillDTO.BillLineItemDTO itemDTO = new BillDTO.BillLineItemDTO();
                itemDTO.setId(item.getId());
                itemDTO.setItemCode(item.getItemCode());
                itemDTO.setDescription(item.getDescription());
                itemDTO.setMaterialFamily(item.getMaterialFamily());
                itemDTO.setQtyOrdered(item.getQtyOrdered());
                itemDTO.setQtyReceived(item.getQtyReceived());
                itemDTO.setUnitPrice(item.getUnitPrice());
                itemDTO.setTotalAmount(item.getTotalAmount());
                itemDTO.setStatus(item.getStatus());
                itemDTO.setLocation(item.getLocation());
                itemDTO.setRemarks(item.getRemarks());
                itemDTO.setQualityCheckRequirement(item.getQualityCheckRequirement());
                itemDTO.setUniqueCode(item.getUniqueCode());
                lineItemDTOs.add(itemDTO);
            }
            dto.setLineItems(lineItemDTOs);
        }

        return dto;
    }

    /**
     * Generate bill number in format PO{vendorPoId}-GN{sequence}
     */
    private String generateBillNumber(String vendorPoId) {
        // Count existing bills for this vendor PO to get sequence
        List<BillEntity> existingBills = billRepository.findByVendorPoId(vendorPoId);
        int sequence = existingBills.size() + 1;
        return "PO" + vendorPoId + "-GN" + sequence;
    }
}
