package com.synergy.service;

import com.synergy.dto.GatePassDTO;
import com.synergy.dto.GatePassLightDTO;
import com.synergy.dto.GatePassFormDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.dto.ApprovedPOLineItemDTO;
import com.synergy.entity.GatePassEntity;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorPODeliveryTermsEntity;
import com.synergy.repository.GatePassRepository;
import com.synergy.repository.PurchaseRequestRepository;
import com.synergy.repository.VendorPODeliveryTermsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Base64;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class GatePassService {

    @Autowired
    private GatePassRepository gatePassRepository;

    @Autowired
    private VendorPODeliveryTermsRepository deliveryTermsRepository;

    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    @Autowired
    private FileStorageService fileStorageService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Get all approved vendor POs that can have gate passes created (OPTIMIZED - Light DTO)
     * This method uses BATCH PROCESSING to improve performance while maintaining the same logic
     */
    public List<GatePassLightDTO> getAllApprovedVendorPOsForGatePassLight() {
        // Get all approved PRs (lightweight query)
        List<PurchaseRequestEntity> approvedPRs = purchaseRequestRepository.findByStatusIgnoreCase("APPROVED");

        if (approvedPRs.isEmpty()) {
            return new ArrayList<>();
        }

        // Extract PR IDs for batch processing
        List<Long> approvedPrIds = approvedPRs.stream()
                .map(PurchaseRequestEntity::getId)
                .collect(Collectors.toList());

        // BATCH QUERY: Get all vendor split POs for all approved PRs in one call
        Map<Long, List<VendorSplitPODTO>> allVendorPOsMap = purchaseRequestService.getBatchApprovedVendorSplitPODetails(approvedPrIds);

        List<GatePassLightDTO> gatePassList = new ArrayList<>();

        // Process each PR using the batch-loaded data
        for (PurchaseRequestEntity pr : approvedPRs) {
            try {
                // Get vendor split POs from batch-loaded data
                List<VendorSplitPODTO> actualVendorPOs = allVendorPOsMap.getOrDefault(pr.getId(), new ArrayList<>());

                // Create gate pass entries only for vendor POs that actually exist
                for (VendorSplitPODTO vendorPO : actualVendorPOs) {
                    String vendorPoId = vendorPO.getVendorPoId();

                    // Check if gate pass already exists for this vendor PO
                    boolean gatePassExists = gatePassRepository.existsByVendorPoId(vendorPoId);

                    if (!gatePassExists) {
                        // Create lightweight gate pass DTO using actual vendor PO data
                        GatePassLightDTO gatePassDTO = createLightGatePassDTOFromVendorPO(vendorPO);
                        if (gatePassDTO != null) {
                            gatePassList.add(gatePassDTO);
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("Skipping PR " + pr.getId() + " for gate pass: " + e.getMessage());
            }
        }

        return gatePassList;
    }

    /**
     * Get all approved vendor POs that can have gate passes created (LEGACY - Full DTO)
     * This uses the same data as the approved vendor POs API but formats it for gate pass dashboard
     */
    public List<GatePassDTO> getAllApprovedVendorPOsForGatePass() {
        // Get all approved vendor POs
        List<VendorSplitPODTO> approvedVendorPOs = purchaseRequestService.getAllApprovedVendorPOs();

        List<GatePassDTO> gatePassList = new ArrayList<>();

        for (VendorSplitPODTO vendorPO : approvedVendorPOs) {
            // Check if gate pass already exists for this vendor PO
            boolean gatePassExists = gatePassRepository.existsByVendorPoId(vendorPO.getVendorPoId());

            if (!gatePassExists) {
                // Create gate pass DTO from vendor PO data
                GatePassDTO gatePassDTO = createGatePassDTOFromVendorPO(vendorPO);
                gatePassList.add(gatePassDTO);
            }
        }

        return gatePassList;
    }
    
    /**
     * Get all existing gate passes
     */
    public List<GatePassDTO> getAllGatePasses() {
        List<GatePassEntity> gatePasses = gatePassRepository.findAllByOrderByCreatedDateDesc();
        return gatePasses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Get gate passes by status
     */
    public List<GatePassDTO> getGatePassesByStatus(String status) {
        List<GatePassEntity> gatePasses = gatePassRepository.findByStatusIgnoreCaseOrderByExpectedDateAsc(status);
        return gatePasses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Create a gate pass for a vendor PO
     */
    public GatePassDTO createGatePass(String vendorPoId) {
        // Check if gate pass already exists
        if (gatePassRepository.existsByVendorPoId(vendorPoId)) {
            throw new IllegalArgumentException("Gate pass already exists for vendor PO: " + vendorPoId);
        }
        
        // Get vendor PO details
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);
        
        // Create gate pass entity
        GatePassEntity gatePass = new GatePassEntity();
        
        // Generate gate pass ID: PO{vendorPoId}-GP{sequence}
        Long sequence = gatePassRepository.getNextSequenceForVendorPo(vendorPoId);
        String gatePassId = "PO" + vendorPoId + "-GP" + String.format("%02d", sequence);
        
        gatePass.setGatePassId(gatePassId);
        gatePass.setVendorPoId(vendorPoId);
        gatePass.setOriginalPrId(vendorPO.getOriginalPrId());
        gatePass.setOriginalPrIdString(vendorPO.getOriginalPrIdString());
        gatePass.setVendorId(vendorPO.getVendorId());
        gatePass.setVendorCompanyName(vendorPO.getVendorCompanyName());
        gatePass.setVendorName(vendorPO.getVendorName());
        gatePass.setVendorEmail(vendorPO.getVendorEmail());
        gatePass.setVendorContactNumber(vendorPO.getVendorContactNumber());
        gatePass.setYardNumber(vendorPO.getYardNumber());
        gatePass.setProjectName(vendorPO.getProjectName());
        gatePass.setContractorName(vendorPO.getContractorName());
        gatePass.setLineItemCount(vendorPO.getLineItemCount());
        
        // Get delivery date from stored delivery terms
        VendorPODeliveryTermsEntity deliveryTerms = deliveryTermsRepository.findByVendorPoId(vendorPoId).orElse(null);
        if (deliveryTerms != null) {
            gatePass.setExpectedDate(deliveryTerms.getDeliveryDate());
        } else {
            // Fallback to current date + 7 days if no delivery terms found
            gatePass.setExpectedDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        }
        
        gatePass.setStatus("PENDING");
        
        // Save gate pass
        GatePassEntity savedGatePass = gatePassRepository.save(gatePass);
        
        return convertToDTO(savedGatePass);
    }
    
    /**
     * Create lightweight gate pass DTO from actual vendor PO data (CONSISTENT WITH VENDOR SPLIT LOGIC)
     */
    private GatePassLightDTO createLightGatePassDTOFromVendorPO(VendorSplitPODTO vendorPO) {
        GatePassLightDTO gatePassDTO = new GatePassLightDTO();

        // Generate potential gate pass ID for display
        String potentialGatePassId = "PO" + vendorPO.getVendorPoId() + "-GP01";

        gatePassDTO.setGatePassId(potentialGatePassId);
        gatePassDTO.setVendorPoId(vendorPO.getVendorPoId());
        gatePassDTO.setOriginalPrId(vendorPO.getOriginalPrId());
        gatePassDTO.setProjectName(vendorPO.getProjectName());
        gatePassDTO.setYardNumber(vendorPO.getYardNumber());
        gatePassDTO.setStatus("AVAILABLE"); // Special status for available gate passes

        // Use actual line item count from vendor PO
        gatePassDTO.setLineItemCount(vendorPO.getLineItemCount());

        // Get delivery date from stored delivery terms (if available)
        VendorPODeliveryTermsEntity deliveryTerms = deliveryTermsRepository.findByVendorPoId(vendorPO.getVendorPoId()).orElse(null);
        if (deliveryTerms != null) {
            gatePassDTO.setExpectedDate(deliveryTerms.getDeliveryDate());
        }
        // If no delivery terms exist, expectedDate will remain null (empty)

        // Use vendor company name from vendor PO
        gatePassDTO.setVendorCompanyName(vendorPO.getVendorCompanyName());

        return gatePassDTO;
    }

    /**
     * Create lightweight gate pass DTO from PR data (LEGACY - KEPT FOR BACKWARD COMPATIBILITY)
     */
    private GatePassLightDTO createLightGatePassDTO(PurchaseRequestEntity pr, String vendorPoId) {
        GatePassLightDTO gatePassDTO = new GatePassLightDTO();

        // Generate potential gate pass ID for display
        String potentialGatePassId = "PO" + vendorPoId + "-GP01";

        gatePassDTO.setGatePassId(potentialGatePassId);
        gatePassDTO.setVendorPoId(vendorPoId);
        gatePassDTO.setOriginalPrId(pr.getId());
        gatePassDTO.setProjectName(pr.getProjectName());
        gatePassDTO.setYardNumber(pr.getYardNumber());
        gatePassDTO.setStatus("AVAILABLE"); // Special status for available gate passes

        // Get line item count (lightweight)
        if (pr.getLineItems() != null) {
            gatePassDTO.setLineItemCount(pr.getLineItems().size());
        } else {
            gatePassDTO.setLineItemCount(0);
        }

        // Get delivery date from stored delivery terms (if available)
        VendorPODeliveryTermsEntity deliveryTerms = deliveryTermsRepository.findByVendorPoId(vendorPoId).orElse(null);
        if (deliveryTerms != null) {
            gatePassDTO.setExpectedDate(deliveryTerms.getDeliveryDate());
        }
        // If no delivery terms exist, expectedDate will remain null (empty)

        // Get vendor company name (if available)
        if (pr.getVendors() != null && !pr.getVendors().isEmpty()) {
            gatePassDTO.setVendorCompanyName(pr.getVendors().get(0).getCompanyName());
        } else {
            gatePassDTO.setVendorCompanyName(pr.getContractorName()); // Fallback for instant PRs
        }

        return gatePassDTO;
    }

    /**
     * Convert VendorSplitPODTO to GatePassDTO for display purposes (LEGACY)
     */
    private GatePassDTO createGatePassDTOFromVendorPO(VendorSplitPODTO vendorPO) {
        GatePassDTO gatePassDTO = new GatePassDTO();
        
        // Generate potential gate pass ID for display
        String potentialGatePassId = "PO" + vendorPO.getVendorPoId() + "-GP01";
        
        gatePassDTO.setGatePassId(potentialGatePassId);
        gatePassDTO.setVendorPoId(vendorPO.getVendorPoId());
        gatePassDTO.setOriginalPrId(vendorPO.getOriginalPrId());
        gatePassDTO.setOriginalPrIdString(vendorPO.getOriginalPrIdString());
        gatePassDTO.setVendorId(vendorPO.getVendorId());
        gatePassDTO.setVendorCompanyName(vendorPO.getVendorCompanyName());
        gatePassDTO.setVendorName(vendorPO.getVendorName());
        gatePassDTO.setVendorEmail(vendorPO.getVendorEmail());
        gatePassDTO.setVendorContactNumber(vendorPO.getVendorContactNumber());
        gatePassDTO.setYardNumber(vendorPO.getYardNumber());
        gatePassDTO.setProjectName(vendorPO.getProjectName());
        gatePassDTO.setContractorName(vendorPO.getContractorName());
        gatePassDTO.setLineItemCount(vendorPO.getLineItemCount());
        gatePassDTO.setStatus("AVAILABLE"); // Special status for available gate passes
        
        // Get delivery date from stored delivery terms
        VendorPODeliveryTermsEntity deliveryTerms = deliveryTermsRepository.findByVendorPoId(vendorPO.getVendorPoId()).orElse(null);
        if (deliveryTerms != null) {
            gatePassDTO.setExpectedDate(deliveryTerms.getDeliveryDate());
        }
        
        return gatePassDTO;
    }
    
    /**
     * Convert GatePassEntity to GatePassDTO
     */
    private GatePassDTO convertToDTO(GatePassEntity entity) {
        GatePassDTO dto = new GatePassDTO();
        dto.setGatePassId(entity.getGatePassId());
        dto.setVendorPoId(entity.getVendorPoId());
        dto.setOriginalPrId(entity.getOriginalPrId());
        dto.setOriginalPrIdString(entity.getOriginalPrIdString());
        dto.setVendorId(entity.getVendorId());
        dto.setVendorCompanyName(entity.getVendorCompanyName());
        dto.setVendorName(entity.getVendorName());
        dto.setVendorEmail(entity.getVendorEmail());
        dto.setVendorContactNumber(entity.getVendorContactNumber());
        dto.setYardNumber(entity.getYardNumber());
        dto.setProjectName(entity.getProjectName());
        dto.setContractorName(entity.getContractorName());
        dto.setExpectedDate(entity.getExpectedDate());
        dto.setLineItemCount(entity.getLineItemCount());
        dto.setStatus(entity.getStatus());
        dto.setCreatedDate(entity.getCreatedDate());
        dto.setIssuedDate(entity.getIssuedDate());
        dto.setCompletedDate(entity.getCompletedDate());
        dto.setRemarks(entity.getRemarks());

        // Calculate validTill (creation time + 3 hours) and isEditable
        if (entity.getCreatedDate() != null) {
            long validTillTime = entity.getCreatedDate().getTime() + (3 * 60 * 60 * 1000L); // 3 hours in milliseconds
            dto.setValidTill(new Date(validTillTime));

            // Check if current time is within the editable window
            long currentTime = System.currentTimeMillis();
            dto.setIsEditable(currentTime <= validTillTime);
        } else {
            dto.setValidTill(null);
            dto.setIsEditable(false);
        }

        // Convert photo paths to URLs for the array
        List<String> vehiclePhotos = new ArrayList<>();
        try {
            if (entity.getVehiclePhotoPaths() != null && !entity.getVehiclePhotoPaths().isEmpty()) {
                // Use new array format
                List<String> photoPaths = objectMapper.readValue(entity.getVehiclePhotoPaths(), List.class);
                for (String photoPath : photoPaths) {
                    vehiclePhotos.add(fileStorageService.getImageUrl(photoPath));
                }
            } else {
                // Fallback to legacy front/back photo fields for backward compatibility
                if (entity.getVehicleFrontPhotoPath() != null) {
                    vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleFrontPhotoPath()));
                }
                if (entity.getVehicleBackPhotoPath() != null) {
                    vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleBackPhotoPath()));
                }
            }
        } catch (Exception e) {
            // Fallback to legacy fields if JSON parsing fails
            if (entity.getVehicleFrontPhotoPath() != null) {
                vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleFrontPhotoPath()));
            }
            if (entity.getVehicleBackPhotoPath() != null) {
                vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleBackPhotoPath()));
            }
        }
        dto.setVehiclePhotos(vehiclePhotos);

        return dto;
    }

    /**
     * Get gate pass form data including line items
     */
    public GatePassFormDTO getGatePassFormData(String vendorPoId) {
        // Check if gate pass already exists for this vendor PO
        List<GatePassEntity> existingGatePasses = gatePassRepository.findByVendorPoId(vendorPoId);

        if (!existingGatePasses.isEmpty()) {
            // Return existing gate pass form data
            GatePassEntity existingGatePass = existingGatePasses.get(0); // Get the first (should be only one)
            return convertEntityToFormDTO(existingGatePass);
        }

        // Get vendor PO details
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);

        // Generate gate pass ID
        Long sequence = gatePassRepository.getNextSequenceForVendorPo(vendorPoId);
        String gatePassId = "PO" + vendorPoId + "-GP" + String.format("%02d", sequence);

        // Create form DTO
        GatePassFormDTO formDTO = new GatePassFormDTO();
        formDTO.setGatePassId(gatePassId);
        formDTO.setVendorPoId(vendorPoId);
        formDTO.setOriginalPrId(vendorPO.getOriginalPrId());
        formDTO.setOriginalPrIdString(vendorPO.getOriginalPrIdString());
        formDTO.setVendorId(vendorPO.getVendorId());
        formDTO.setVendorCompanyName(vendorPO.getVendorCompanyName());
        formDTO.setVendorName(vendorPO.getVendorName());
        formDTO.setVendorEmail(vendorPO.getVendorEmail());
        formDTO.setVendorContactNumber(vendorPO.getVendorContactNumber());
        formDTO.setYardNumber(vendorPO.getYardNumber());
        formDTO.setProjectName(vendorPO.getProjectName());
        formDTO.setContractorName(vendorPO.getContractorName());
        formDTO.setLineItemCount(vendorPO.getLineItemCount());

        // Get delivery date from stored delivery terms
        VendorPODeliveryTermsEntity deliveryTerms = deliveryTermsRepository.findByVendorPoId(vendorPoId).orElse(null);
        if (deliveryTerms != null) {
            formDTO.setExpectedDate(deliveryTerms.getDeliveryDate());
        }

        // Get line items from vendor split PO
        List<GatePassFormDTO.GatePassLineItemDTO> lineItems = new ArrayList<>();
        if (vendorPO.getLineItems() != null) {
            for (ApprovedPOLineItemDTO item : vendorPO.getLineItems()) {
                GatePassFormDTO.GatePassLineItemDTO lineItem = new GatePassFormDTO.GatePassLineItemDTO();
                lineItem.setUniqueCode(item.getUniqueCode()); // Add unique code
                lineItem.setItemCode(item.getItemCode());
                lineItem.setDescription(item.getDescription());
                lineItem.setMaterialFamily(item.getMaterialFamily());
                lineItem.setQuantity(item.getQuantity() != null ? item.getQuantity().doubleValue() : null);
                lineItem.setUnit(item.getUnitOfMeasure());
                lineItem.setRate(item.getRateApproved() != null ? item.getRateApproved().doubleValue() : null);
                lineItem.setAmount(item.getTotal() != null ? item.getTotal().doubleValue() : null);
                lineItems.add(lineItem);
            }
        }
        formDTO.setLineItems(lineItems);

        return formDTO;
    }

    /**
     * Convert GatePassEntity to GatePassFormDTO with all form fields populated
     */
    private GatePassFormDTO convertEntityToFormDTO(GatePassEntity entity) {
        GatePassFormDTO formDTO = new GatePassFormDTO();

        // Basic gate pass information
        formDTO.setGatePassId(entity.getGatePassId());
        formDTO.setVendorPoId(entity.getVendorPoId());
        formDTO.setOriginalPrId(entity.getOriginalPrId());
        formDTO.setOriginalPrIdString(entity.getOriginalPrIdString());
        formDTO.setVendorId(entity.getVendorId());
        formDTO.setVendorCompanyName(entity.getVendorCompanyName());
        formDTO.setVendorName(entity.getVendorName());
        formDTO.setVendorEmail(entity.getVendorEmail());
        formDTO.setVendorContactNumber(entity.getVendorContactNumber());
        formDTO.setYardNumber(entity.getYardNumber());
        formDTO.setProjectName(entity.getProjectName());
        formDTO.setContractorName(entity.getContractorName());
        formDTO.setExpectedDate(entity.getExpectedDate());
        formDTO.setLineItemCount(entity.getLineItemCount());

        // Form fields - these are the fields that were missing
        formDTO.setInvoiceNumber(entity.getInvoiceNumber());
        formDTO.setVehicleNumber(entity.getVehicleNumber());
        formDTO.setDriverName(entity.getDriverName());

        // Convert photo paths to URLs for the array
        List<String> vehiclePhotos = new ArrayList<>();
        try {
            if (entity.getVehiclePhotoPaths() != null && !entity.getVehiclePhotoPaths().isEmpty()) {
                // Use new array format
                List<String> photoPaths = objectMapper.readValue(entity.getVehiclePhotoPaths(), List.class);
                for (String photoPath : photoPaths) {
                    vehiclePhotos.add(fileStorageService.getImageUrl(photoPath));
                }
            } else {
                // Fallback to legacy front/back photo fields for backward compatibility
                if (entity.getVehicleFrontPhotoPath() != null) {
                    vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleFrontPhotoPath()));
                }
                if (entity.getVehicleBackPhotoPath() != null) {
                    vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleBackPhotoPath()));
                }
            }
        } catch (Exception e) {
            // Fallback to legacy fields if JSON parsing fails
            if (entity.getVehicleFrontPhotoPath() != null) {
                vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleFrontPhotoPath()));
            }
            if (entity.getVehicleBackPhotoPath() != null) {
                vehiclePhotos.add(fileStorageService.getImageUrl(entity.getVehicleBackPhotoPath()));
            }
        }
        formDTO.setVehiclePhotos(vehiclePhotos);

        // Get line items from vendor split PO (since they're not stored in gate pass entity)
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(entity.getVendorPoId());
        List<GatePassFormDTO.GatePassLineItemDTO> lineItems = new ArrayList<>();
        if (vendorPO.getLineItems() != null) {
            for (ApprovedPOLineItemDTO item : vendorPO.getLineItems()) {
                GatePassFormDTO.GatePassLineItemDTO lineItem = new GatePassFormDTO.GatePassLineItemDTO();
                lineItem.setUniqueCode(item.getUniqueCode());
                lineItem.setItemCode(item.getItemCode());
                lineItem.setDescription(item.getDescription());
                lineItem.setMaterialFamily(item.getMaterialFamily());
                lineItem.setQuantity(item.getQuantity() != null ? item.getQuantity().doubleValue() : null);
                lineItem.setUnit(item.getUnitOfMeasure());
                lineItem.setRate(item.getRateApproved() != null ? item.getRateApproved().doubleValue() : null);
                lineItem.setAmount(item.getTotal() != null ? item.getTotal().doubleValue() : null);
                lineItems.add(lineItem);
            }
        }
        formDTO.setLineItems(lineItems);

        return formDTO;
    }

    /**
     * Submit gate pass form with photos and create gate pass
     */
    public GatePassEntity submitGatePassForm(String vendorPoId, Map<String, Object> requestData) {
        // Check if gate pass already exists
        if (gatePassRepository.existsByVendorPoId(vendorPoId)) {
            throw new IllegalArgumentException("Gate pass already exists for vendor PO: " + vendorPoId);
        }

        // Get vendor PO details
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);

        // Create gate pass entity
        GatePassEntity gatePass = new GatePassEntity();

        // Generate gate pass ID
        Long sequence = gatePassRepository.getNextSequenceForVendorPo(vendorPoId);
        String gatePassId = "PO" + vendorPoId + "-GP" + String.format("%02d", sequence);

        gatePass.setGatePassId(gatePassId);
        gatePass.setVendorPoId(vendorPoId);
        gatePass.setOriginalPrId(vendorPO.getOriginalPrId());
        gatePass.setOriginalPrIdString(vendorPO.getOriginalPrIdString());
        gatePass.setVendorId(vendorPO.getVendorId());
        gatePass.setVendorCompanyName(vendorPO.getVendorCompanyName());
        gatePass.setVendorName(vendorPO.getVendorName());
        gatePass.setVendorEmail(vendorPO.getVendorEmail());
        gatePass.setVendorContactNumber(vendorPO.getVendorContactNumber());
        gatePass.setYardNumber(vendorPO.getYardNumber());
        gatePass.setProjectName(vendorPO.getProjectName());
        gatePass.setContractorName(vendorPO.getContractorName());
        gatePass.setLineItemCount(vendorPO.getLineItemCount());

        // Get delivery date from stored delivery terms
        VendorPODeliveryTermsEntity deliveryTerms = deliveryTermsRepository.findByVendorPoId(vendorPoId).orElse(null);
        if (deliveryTerms != null) {
            gatePass.setExpectedDate(deliveryTerms.getDeliveryDate());
        }

        // Set form data
        gatePass.setInvoiceNumber((String) requestData.get("invoiceNumber"));
        gatePass.setVehicleNumber((String) requestData.get("vehicleNumber"));
        gatePass.setDriverName((String) requestData.get("driverName"));

        // Process vehicle photos
        try {
            List<String> vehiclePhotos = (List<String>) requestData.get("vehiclePhotos");

            if (vehiclePhotos != null && !vehiclePhotos.isEmpty()) {
                List<String> photoPaths = new ArrayList<>();

                for (int i = 0; i < vehiclePhotos.size(); i++) {
                    String photoData = vehiclePhotos.get(i);
                    if (photoData != null && !photoData.isEmpty()) {
                        String photoPath = saveBase64Image(photoData, "vehicle_" + i);
                        photoPaths.add(photoPath);

                        // Maintain backward compatibility with front/back photo fields
                        if (i == 0) {
                            gatePass.setVehicleFrontPhotoPath(photoPath);
                        } else if (i == 1) {
                            gatePass.setVehicleBackPhotoPath(photoPath);
                        }
                    }
                }

                // Store all photo paths as JSON
                if (!photoPaths.isEmpty()) {
                    gatePass.setVehiclePhotoPaths(objectMapper.writeValueAsString(photoPaths));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to save vehicle photos: " + e.getMessage());
        }

        // Set status to ISSUED and set issued date
        gatePass.setStatus("ISSUED");
        gatePass.setIssuedDate(new Date());

        // Save gate pass
        return gatePassRepository.save(gatePass);
    }

    /**
     * Get issued gate passes with photo URLs
     */
    public List<Map<String, Object>> getIssuedGatePassesWithPhotos() {
        List<GatePassEntity> issuedGatePasses = gatePassRepository.findByStatusIgnoreCaseOrderByIssuedDateDesc("ISSUED");

        List<Map<String, Object>> result = new ArrayList<>();
        for (GatePassEntity gatePass : issuedGatePasses) {
            Map<String, Object> gatePassData = new HashMap<>();
            gatePassData.put("gatePassId", gatePass.getGatePassId());
            gatePassData.put("vendorPoId", gatePass.getVendorPoId()); // Added vendor PO ID
            gatePassData.put("actualDate", gatePass.getIssuedDate());
            gatePassData.put("expectedDate", gatePass.getExpectedDate());
            gatePassData.put("projectName", gatePass.getProjectName());
            gatePassData.put("yardNumber", gatePass.getYardNumber());
            gatePassData.put("lineItems", gatePass.getLineItemCount());
            gatePassData.put("invoiceNumber", gatePass.getInvoiceNumber());
            gatePassData.put("vehicleNumber", gatePass.getVehicleNumber());
            gatePassData.put("driverName", gatePass.getDriverName());

            // Add vendor information
            gatePassData.put("vendorCompanyName", gatePass.getVendorCompanyName());
            gatePassData.put("vendorName", gatePass.getVendorName());

            // Calculate and add validTill and isEditable fields
            if (gatePass.getCreatedDate() != null) {
                long validTillTime = gatePass.getCreatedDate().getTime() + (3 * 60 * 60 * 1000L); // 3 hours in milliseconds
                Date validTill = new Date(validTillTime);
                gatePassData.put("validTill", validTill);

                // Check if current time is within the editable window
                long currentTime = System.currentTimeMillis();
                boolean isEditable = currentTime <= validTillTime;
                gatePassData.put("isEditable", isEditable);
            } else {
                gatePassData.put("validTill", null);
                gatePassData.put("isEditable", false);
            }

            // Add photo URLs as array
            List<String> vehiclePhotoUrls = new ArrayList<>();
            try {
                if (gatePass.getVehiclePhotoPaths() != null && !gatePass.getVehiclePhotoPaths().isEmpty()) {
                    // Use new array format
                    List<String> photoPaths = objectMapper.readValue(gatePass.getVehiclePhotoPaths(), List.class);
                    for (String photoPath : photoPaths) {
                        vehiclePhotoUrls.add(fileStorageService.getImageUrl(photoPath));
                    }
                } else {
                    // Fallback to legacy front/back photo fields for backward compatibility
                    if (gatePass.getVehicleFrontPhotoPath() != null) {
                        vehiclePhotoUrls.add(fileStorageService.getImageUrl(gatePass.getVehicleFrontPhotoPath()));
                    }
                    if (gatePass.getVehicleBackPhotoPath() != null) {
                        vehiclePhotoUrls.add(fileStorageService.getImageUrl(gatePass.getVehicleBackPhotoPath()));
                    }
                }
            } catch (Exception e) {
                // Fallback to legacy fields if JSON parsing fails
                if (gatePass.getVehicleFrontPhotoPath() != null) {
                    vehiclePhotoUrls.add(fileStorageService.getImageUrl(gatePass.getVehicleFrontPhotoPath()));
                }
                if (gatePass.getVehicleBackPhotoPath() != null) {
                    vehiclePhotoUrls.add(fileStorageService.getImageUrl(gatePass.getVehicleBackPhotoPath()));
                }
            }
            gatePassData.put("vehiclePhotoUrls", vehiclePhotoUrls);

            result.add(gatePassData);
        }

        return result;
    }

    /**
     * Get gate pass by gate pass ID
     */
    public GatePassDTO getGatePassById(String gatePassId) {
        GatePassEntity gatePass = gatePassRepository.findByGatePassId(gatePassId)
                .orElseThrow(() -> new IllegalArgumentException("Gate pass not found with ID: " + gatePassId));
        return convertToDTO(gatePass);
    }

    /**
     * Update gate pass
     */
    public GatePassDTO updateGatePass(String gatePassId, Map<String, Object> updateData) {
        GatePassEntity gatePass = gatePassRepository.findByGatePassId(gatePassId)
                .orElseThrow(() -> new IllegalArgumentException("Gate pass not found with ID: " + gatePassId));

        // Check if gate pass is editable (within 3 hours of creation)
        if (gatePass.getCreatedDate() != null) {
            long validTillTime = gatePass.getCreatedDate().getTime() + (3 * 60 * 60 * 1000L);
            long currentTime = System.currentTimeMillis();
            if (currentTime > validTillTime) {
                throw new IllegalArgumentException("Gate pass is no longer editable. Edit window has expired.");
            }
        }

        // Update allowed fields
        if (updateData.containsKey("invoiceNumber")) {
            gatePass.setInvoiceNumber((String) updateData.get("invoiceNumber"));
        }
        if (updateData.containsKey("vehicleNumber")) {
            gatePass.setVehicleNumber((String) updateData.get("vehicleNumber"));
        }
        if (updateData.containsKey("driverName")) {
            gatePass.setDriverName((String) updateData.get("driverName"));
        }
        if (updateData.containsKey("remarks")) {
            gatePass.setRemarks((String) updateData.get("remarks"));
        }
        if (updateData.containsKey("status")) {
            String newStatus = (String) updateData.get("status");
            if ("PENDING".equals(newStatus) || "ISSUED".equals(newStatus) || "COMPLETED".equals(newStatus)) {
                gatePass.setStatus(newStatus);
                if ("COMPLETED".equals(newStatus) && gatePass.getCompletedDate() == null) {
                    gatePass.setCompletedDate(new Date());
                }
            }
        }

        // Process vehicle photos if provided
        try {
            if (updateData.containsKey("vehiclePhotos")) {
                List<String> vehiclePhotos = (List<String>) updateData.get("vehiclePhotos");

                if (vehiclePhotos != null && !vehiclePhotos.isEmpty()) {
                    List<String> photoPaths = new ArrayList<>();

                    for (int i = 0; i < vehiclePhotos.size(); i++) {
                        String photoData = vehiclePhotos.get(i);
                        if (photoData != null && !photoData.isEmpty()) {
                            String photoPath = saveBase64Image(photoData, "vehicle_" + i);
                            photoPaths.add(photoPath);

                            // Maintain backward compatibility with front/back photo fields
                            if (i == 0) {
                                gatePass.setVehicleFrontPhotoPath(photoPath);
                            } else if (i == 1) {
                                gatePass.setVehicleBackPhotoPath(photoPath);
                            }
                        }
                    }

                    // Store all photo paths as JSON
                    if (!photoPaths.isEmpty()) {
                        gatePass.setVehiclePhotoPaths(objectMapper.writeValueAsString(photoPaths));
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to save vehicle photos: " + e.getMessage());
        }

        GatePassEntity updatedGatePass = gatePassRepository.save(gatePass);
        return convertToDTO(updatedGatePass);
    }

    /**
     * Delete gate pass
     */
    public void deleteGatePass(String gatePassId) {
        GatePassEntity gatePass = gatePassRepository.findByGatePassId(gatePassId)
                .orElseThrow(() -> new IllegalArgumentException("Gate pass not found with ID: " + gatePassId));

        // Check if gate pass can be deleted (only PENDING status can be deleted)
        if (!"PENDING".equals(gatePass.getStatus())) {
            throw new IllegalArgumentException("Only gate passes with PENDING status can be deleted. Current status: " + gatePass.getStatus());
        }

        gatePassRepository.delete(gatePass);
    }

    /**
     * Save base64 image and return file path
     */
    private String saveBase64Image(String base64Image, String prefix) throws Exception {
        // Extract image data from base64 string
        String[] parts = base64Image.split(",");
        String imageType = "png"; // Default to png

        // Try to extract the image type from the base64 string
        if (parts.length > 1 && parts[0].contains(":") && parts[0].contains(";")) {
            imageType = parts[0].split(":")[1].split(";")[0].split("/")[1];
        }

        // Decode the base64 string
        byte[] imageBytes;
        if (parts.length > 1) {
            imageBytes = Base64.getDecoder().decode(parts[1]);
        } else {
            imageBytes = Base64.getDecoder().decode(base64Image);
        }

        // Generate a unique filename
        String fileName = fileStorageService.generateUniqueFilename(prefix, imageType);
        Path uploadPath = fileStorageService.getUploadPath();

        // Create directory if it doesn't exist
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }

        // Save the file
        Path filePath = uploadPath.resolve(fileName);
        Files.write(filePath, imageBytes);

        return fileName;
    }

    public GatePassDTO getGatePassForm(String id) {
        GatePassEntity gatePass = gatePassRepository.findById(Long.valueOf(id))
            .orElseThrow(() -> new IllegalArgumentException("Gate pass not found"));

        return convertToDTO(gatePass);
    }
}
