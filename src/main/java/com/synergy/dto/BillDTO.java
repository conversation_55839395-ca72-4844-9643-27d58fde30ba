package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for bill details")
public class BillDTO {
    
    @Schema(description = "Bill ID", example = "1")
    private Long id;
    
    @Schema(description = "Bill number", example = "BILL0001")
    private String billNumber;
    
    @Schema(description = "GRN ID", example = "1")
    private Long grnId;
    
    @Schema(description = "GRN number", example = "GRN0001")
    private String grnNumber;
    
    @Schema(description = "Vendor PO ID", example = "647-1")
    private String vendorPoId;
    
    @Schema(description = "Yard number", example = "YD-101")
    private String yardNumber;
    
    @Schema(description = "Project name", example = "Speed Boat")
    private String projectName;
    
    @Schema(description = "Vendor company name", example = "ABC Steel Suppliers")
    private String vendorCompanyName;
    
    @Schema(description = "Total amount", example = "15000.00")
    private BigDecimal totalAmount;
    
    @Schema(description = "Bill status", example = "DRAFT")
    private String status;
    
    @Schema(description = "Created date", example = "2025-01-15T10:30:00.000Z")
    private Date createdDate;
    
    @Schema(description = "Updated date", example = "2025-01-15T11:30:00.000Z")
    private Date updatedDate;
    
    @Schema(description = "Approved date", example = "2025-01-15T12:30:00.000Z")
    private Date approvedDate;
    
    @Schema(description = "Paid date", example = "2025-01-15T13:30:00.000Z")
    private Date paidDate;
    
    @Schema(description = "Remarks", example = "Bill created from GRN")
    private String remarks;

    @Schema(description = "Number of line items", example = "10")
    private Integer lineItemCount;

    @Schema(description = "List of line items for bill")
    private List<BillLineItemDTO> lineItems;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Bill line item details")
    public static class BillLineItemDTO {
        
        @Schema(description = "Line item ID", example = "1")
        private Long id;
        
        @Schema(description = "Item code", example = "01")
        private String itemCode;
        
        @Schema(description = "Product/Item description", example = "High-strength marine steel")
        private String description;
        
        @Schema(description = "Material family", example = "Steel Plates")
        private String materialFamily;
        
        @Schema(description = "Quantity ordered", example = "13")
        private Integer qtyOrdered;
        
        @Schema(description = "Quantity received", example = "10")
        private Integer qtyReceived;
        
        @Schema(description = "Unit price", example = "1500.00")
        private BigDecimal unitPrice;
        
        @Schema(description = "Total amount", example = "15000.00")
        private BigDecimal totalAmount;
        
        @Schema(description = "Status", example = "Passed")
        private String status;
        
        @Schema(description = "Location", example = "Yard")
        private String location;
        
        @Schema(description = "Remarks", example = "Good condition")
        private String remarks;
        
        @Schema(description = "Quality check requirement for this item", example = "not required")
        private String qualityCheckRequirement;
        
        @Schema(description = "Unique code for the item", example = "UC001")
        private String uniqueCode;
    }
}
